import asyncio
import os
from agno.agent import Agent
from agno.tools.mcp import MCPTools
from mcp import StdioServerParameters
from textwrap import dedent
from agno.models.anthropic import Claude

os.environ['ANTHROPIC_API_KEY'] = ('sk-ant-api03'
                                   '-j1aBToLYOV4lgonQuYRT513SkGaTVOCOdvUC_e8L8VprdydSnQmFTGY0q9WEmx97Hx259JEO9i'
                                   '-A72MoUS927g--llBtQAA')

os.environ['AGNO_DEBUG'] = 'true'

def load_template(template_name: str) -> str:
    path = os.path.join("prompts", template_name)
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


async def run_agent() -> None:
    filesystem_server_params = StdioServerParameters(
        command="npx",
        args=["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/workspace/union"],
    )

    env = {
        "MYSQL_HOST": "localhost",
        "MYSQL_USER": "root",
        "MYSQL_PASSWORD": "root12345",
        "MYSQL_READONLY": "true"
    }

    mysql_server_params = StdioServerParameters(
        command="uvx",
        args=["mysqldb-mcp-server"],
        env=env
    )

    async with MCPTools(server_params=filesystem_server_params) as filesystem_mcp_tools, \
            MCPTools(server_params=mysql_server_params) as mysql_mcp_tools:
        # Create the agent with both MCP tools
        agent = Agent(
            model=Claude(id="claude-3-7-sonnet-20250219"),
            tools=[filesystem_mcp_tools, mysql_mcp_tools],
            markdown=True,
            debug_mode=True,
            show_tool_calls=True,
            description=dedent("""\
                    You are working AI Agent for Morrow, a B2C platform that enables everyone to run agentic tasks through a simple UI.
                    """),

        )

        agent_prompt = load_template("agent.prompt")

        # Use the agent
        await agent.aprint_response(agent_prompt, stream=True)


# Run the agent
if __name__ == "__main__":
    asyncio.run(run_agent())
