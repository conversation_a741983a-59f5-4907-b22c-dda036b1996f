Todos:
1. IMPROVE ELIGILIBY Prompt to detect missing apps and missing context - Max
2. Improving naming of prompts - Max

2. Add GMAIL with accessToken
2. What do to when user 'read my email' without explicity state what email?
2. Authorization!
3. App ID - Gmail, Drive, Sheets, Calendar, Analytics, Tasks, Docs, Translation, Contacts, Ads, YouTube
4. Multi Thread Runner MCP
5. MCP<>Agent File Bridge

Improve ## Error Handling and Eligiblity Check
LISTED MCP SERVERS!
- mysqldb
- github
- supabase
- monday
- notion
- brave
- fetch
- slack
- time

TOP MCP SERVERS!
- Gmail
- Google Calendar
- Google Contacts
- Monday
- WhatsApp (Morrow Bot)
- WhatsApp Business (Bird)
- Slack
- Notion
- Discord
- Supabase @
- Telegram
- Trello
- Asana
- <PERSON>ra
- ClickHouse
- Google Analytics
- Mongo Atlas
- AWS (https://github.com/awslabs/mcp)
- Playwright