# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Docker
Dockerfile
.dockerignore

# Logs
*.log

# Local development
.env
.env.local
.env.*.local

# Test coverage
.coverage
htmlcov/

# Documentation
docs/
*.md
!README.md 