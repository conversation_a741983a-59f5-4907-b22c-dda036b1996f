# MongoDB Settings
MONGODB_HOST=your-mongodb-host
MONGODB_USER=your-mongodb-user
MONGODB_PASSWORD=your-mongodb-password
MONGODB_DB_NAME=morrow

# API Settings
PORT=8080
HOST=0.0.0.0

# Core Settings
ANTHROPIC_API_KEY=your-anthropic-api-key
AGNO_DEBUG=false

# GCP Settings
GCP_PROJECT_ID=your-gcp-project-id
GCP_LOCATION=us-central1
GCP_TASKS_QUEUE=your-tasks-queue
GCP_AGENT_RUNNER_SERVICE_ENDPOINT=https://your-agent-runner-service.run.app

# OAuth Settings
OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
OAUTH_REDIRECT_URI=http://localhost:8080/v1/oauth/callback
OAUTH_FRONTEND_URL=http://localhost:3000
