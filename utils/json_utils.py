import json
import re
from typing import Any

def extract_json_from_text(text: str) -> str:
    """Extract JSON from text that might contain markdown or other formatting"""
    # Remove markdown code block syntax if present
    text = re.sub(r'```json\s*', '', text)
    text = re.sub(r'```\s*$', '', text)
    
    # Find the first { and last } to extract just the JSON
    start = text.find('{')
    end = text.rfind('}') + 1
    
    if start >= 0 and end > start:
        return text[start:end]
    
    # If no JSON found, return the original text
    return text


def safe_json_loads(json_str: str, error_context: str) -> Any:
    """Safely parse JSON with detailed error reporting"""
    if not json_str or json_str.strip() == "":
        raise ValueError(f"Empty JSON string in {error_context}")
    
    # Try to extract <PERSON><PERSON><PERSON> from the text
    json_str = extract_json_from_text(json_str)
    
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        print(f"JSON parsing error in {error_context}: {str(e)}")
        print(f"JSON string: '{json_str}'")
        raise 