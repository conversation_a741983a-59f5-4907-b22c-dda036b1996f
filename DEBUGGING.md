# Debugging Guide for Morrow API

This guide explains how to debug the Morrow API application in Cursor.

## Setting Breakpoints

1. Open the file you want to debug (e.g., `app/routers/workflow.py`)
2. Click in the gutter (the space to the left of the line numbers) to set a breakpoint
3. A red dot will appear, indicating a breakpoint has been set

## Starting a Debug Session

### Method 1: Using the Debug Configuration with Poetry

1. Click on the "Run and Debug" icon in the sidebar (or press `Ctrl+Shift+D`)
2. Select "Python: FastAPI (Poetry)" from the dropdown at the top
3. Click the green play button or press `F5` to start debugging

This will run your application using Poetry, ensuring all dependencies are properly loaded.

### Method 2: Debugging a Specific File with Poetry

1. Open the file you want to debug
2. Click on the "Run and Debug" icon in the sidebar
3. Select "Python: Current File (Poetry)" from the dropdown
4. Click the green play button or press `F5`

### Method 3: Using Poetry Run in Terminal

You can also run your application with Poetry directly in the terminal:

```bash
poetry run python -m debugpy --wait-for-client --listen 5678 api.py
```

Then attach the debugger in Cursor by creating a new launch configuration:

```json
{
    "name": "Python: Attach",
    "type": "python",
    "request": "attach",
    "connect": {
        "host": "localhost",
        "port": 5678
    }
}
```

## Debugging Controls

Once the debugger hits a breakpoint, you can use these controls:

- **Continue (F5)**: Resume execution until the next breakpoint
- **Step Over (F10)**: Execute the current line and move to the next line
- **Step Into (F11)**: Step into a function call
- **Step Out (Shift+F11)**: Step out of the current function
- **Restart (Ctrl+Shift+F5)**: Restart the debugging session
- **Stop (Shift+F5)**: End the debugging session

## Inspecting Variables

When the debugger is paused at a breakpoint:

1. The "Variables" panel shows all local variables
2. Hover over a variable in the code to see its value
3. Use the "Watch" panel to add expressions to monitor

## Debugging Tips

1. **Add print statements**: For quick debugging, add `print()` statements to log values
2. **Use logging**: For more structured logging, use Python's `logging` module
3. **Check the console**: The integrated terminal shows stdout and stderr
4. **Inspect HTTP requests**: Use tools like Postman or curl to test API endpoints

## Common Issues

### Missing Dependencies

If you encounter "ModuleNotFoundError" like `No module named 'motor'`:

1. Make sure you're using Poetry to run the application
2. Check that all dependencies are installed: `poetry install`
3. Verify that the dependency is listed in `pyproject.toml`

### MongoDB Connection Issues

If you encounter MongoDB connection issues:

1. Check that MongoDB is running
2. Verify your connection string in `.env`
3. Add breakpoints in `app/database/mongodb.py` to debug connection

### Validation Errors

For Pydantic validation errors:

1. Add breakpoints in the route handlers
2. Inspect the incoming request data
3. Check the model definitions in `app/models/`

## Example: Debugging the Workflow Endpoints

To debug the workflow endpoints:

1. Set a breakpoint in `app/routers/workflow.py` at the start of the `start_workflow` function
2. Start the debugger with "Python: FastAPI (Poetry)"
3. Make a request to the `/v1/workflow/start` endpoint
4. When the breakpoint is hit, inspect the `request` variable
5. Step through the function to see how it processes the request 