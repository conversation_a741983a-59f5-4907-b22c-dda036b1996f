[{"name": "mysqldb", "tags": ["mysql", "database", "query"], "description": "MySQL server with readonly access and tools for executing SQL queries.", "transport": "stdio", "instructions": "", "command": "uvx", "args": ["mysqldb-mcp-server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "root12345", "MYSQL_READONLY": "true"}}, {"name": "github", "tags": ["github", "code", "git", "repository"], "description": "Access the GitHub API, enabling file operations, repository management, search functionality, and more.", "transport": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, {"name": "supabase", "tags": ["supabase", "database", "query", "lovable"], "description": "Access the Supabase Service, enabling database operations, fetch data, and more.", "transport": "stdio", "command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "<personal-access-token>"], "env": {"PERONAL_ACCESS_TOKEN": ""}}, {"name": "monday", "tags": ["monday", "monday.com", "crm", "tasks", "crm"], "description": "Server for monday.com, enabling to interact with Monday.com boards, items, updates, and documents.", "transport": "stdio", "command": "uvx", "args": ["mcp-server-monday"], "env": {"MONDAY_API_KEY": "your-monday-api-key", "MONDAY_WORKSPACE_NAME": "your-monday-workspace-name"}}, {"name": "notion", "tags": ["notion"], "description": "Server for Notion, enabling to interact with Notion workspaces API", "transport": "stdio", "command": "npx", "args": ["-y", "@suekou/mcp-notion-server"], "env": {"NOTION_API_TOKEN": "your-integration-token"}}, {"name": "brave-search", "tags": ["brave", "search", "google"], "description": "server implementation that integrates the Brave Search API, providing both web and local search capabilities", "transport": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-YOUR_API_KEY_HERE-token"}}, {"name": "fetch", "tags": ["search", "fetch", "website"], "description": "server that provides web content fetching capabilities. This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.", "command": "uvx", "args": ["mcp-server-fetch"]}, {"name": "slack", "tags": ["slack"], "description": "Server for the Slack API, enabling interaction with Slack workspaces", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "xoxb-your-bot-token", "SLACK_TEAM_ID": "T01234567"}}, {"name": "time", "tags": ["time", "clock"], "description": "Server that provides time and timezone conversion capabilities. This server enables getting current time information and perform timezone conversions using IANA timezone names, with automatic system timezone detection", "command": "uvx", "args": ["mcp-server-time"]}, {"name": "airbnb", "tags": ["airbnb", "real-estate"], "description": "Server for searching Airbnb and get listing details.", "command": "npx", "args": ["-y", "@openbnb/mcp-server-airbnb", "--ignore-robots-txt"]}, {"name": "airtable", "tags": ["airtable"], "description": "Server that provides read and write access to Airtable databases", "command": "npx", "args": ["-y", "airtable-mcp-server"], "env": {"AIRTABLE_API_KEY": "pat123.abc123"}}]