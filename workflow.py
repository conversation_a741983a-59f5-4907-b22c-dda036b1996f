import json
import asyncio
import os
import time
import re
from typing import Dict, <PERSON>, Tu<PERSON>, Optional, List
from textwrap import dedent

from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from agno.run.response import RunResponse
from utils.json_utils import safe_json_loads

# Set API keys
os.environ['ANTHROPIC_API_KEY'] = ('sk-ant-api03'
                                   '-j1aBToLYOV4lgonQuYRT513SkGaTVOCOdvUC_e8L8VprdydSnQmFTGY0q9WEmx97Hx259JEO9i'
                                   '-A72MoUS927g--llBtQAA')
# Add your OpenAI API key here if needed
# os.environ['OPENAI_API_KEY'] = 'your-openai-api-key'

# Enable debug mode
os.environ['AGNO_DEBUG'] = 'true'

# Maximum number of retries for each step
MAX_RETRIES = 3
# Delay between retries in seconds
RETRY_DELAY = 2

class WorkflowResult:
    """Class to store the results of the workflow"""
    def __init__(self):
        self.original_user_input = ""
        self.original_user_input_language = ""
        self.user_input_english_text = ""
        self.timing = None  # Store as Python object, not string
        self.cleaned_task_prompt = ""
        self.detect_apps = None  # Store as Python object, not string
        self.agent_prompt = ""
        self.errors = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert the result to a dictionary"""
        return {
            "original_user_input": self.original_user_input,
            "original_user_input_language": self.original_user_input_language,
            "user_input_english_text": self.user_input_english_text,
            "timing": self.timing,  # Will be serialized properly by json.dumps
            "cleaned_task_prompt": self.cleaned_task_prompt,
            "detect_apps": self.detect_apps,  # Will be serialized properly by json.dumps
            "agent_prompt": self.agent_prompt,
            "errors": self.errors
        }

    def __str__(self) -> str:
        """Convert the result to a string"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)  # Preserve non-ASCII characters


def load_template(template_name: str) -> str:
    """Load a template from the prompts directory"""
    path = os.path.join("prompts", template_name)
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def compile_prompt(template_file: str, vars: dict) -> str:
    """Compile a prompt template with variables"""
    template = load_template(template_file)

    # Simple replace: only replace the keys you provide
    for key, value in vars.items():
        template = template.replace(f"{{{key}}}", value)

    return template


class WorkflowAgent:
    """Class to handle the workflow of agents"""

    def __init__(self):
        # Define the models to use, with fallbacks
        self.models = [
            Claude(id="claude-3-7-sonnet-20250219"),
            Claude(id="claude-3-5-sonnet-20241022"),
            # Add OpenAI models as fallbacks if needed
            # OpenAIChat(id="gpt-4o"),
            # OpenAIChat(id="gpt-4-turbo"),
        ]
        self.current_model_index = 0

    def get_next_model(self):
        """Get the next model to use as a fallback"""
        self.current_model_index = (self.current_model_index + 1) % len(self.models)
        return self.models[self.current_model_index]

    def reset_model(self):
        """Reset to the first model"""
        self.current_model_index = 0

    async def run_agent_with_retry(self, prompt: str, step_name: str) -> Tuple[RunResponse, List[str]]:
        """Run an agent with retries and model fallbacks"""
        errors = []
        retries = 0

        while retries < MAX_RETRIES:
            try:
                # Get the current model
                model = self.models[self.current_model_index]

                # Create the agent
                agent = Agent(
                    model=model,
                    description=dedent("""\
                    You are working AI Agent for Morrow, a B2C platform that enables everyone to run agentic tasks through a simple UI.
                    """),
                    markdown=False,
                )

                # Run the agent
                response = await agent.arun(prompt)

                # Debug: Print the response content
                print(f"\n{step_name} Response (attempt {retries + 1}):")
                print(f"Response messages: {len(response.messages)}")
                if len(response.messages) >= 3:
                    print(f"Content: {response.messages[2].content}")
                else:
                    print("Not enough messages in response")

                return response, errors

            except Exception as e:
                error_msg = f"Error in {step_name} (attempt {retries + 1}/{MAX_RETRIES}): {str(e)}"
                print(error_msg)
                errors.append(error_msg)

                # Try with a different model
                self.get_next_model()

                # Wait before retrying
                await asyncio.sleep(RETRY_DELAY)
                retries += 1

        # If we've exhausted all retries, raise an exception
        raise Exception(f"Failed to run {step_name} after {MAX_RETRIES} attempts: {errors}")

    async def normalized_user_input(self, user_input: str) -> Tuple[str, str, List[str]]:
        """Step 1: Translate user input to English"""
        errors = []

        try:
            # Compile the prompt
            translated_input_prompt = compile_prompt("translate.prompt", {"user_input": user_input})
            print('\ntranslated_input: ' + translated_input_prompt)

            # Run the agent
            user_input_english, step_errors = await self.run_agent_with_retry(translated_input_prompt, "Translation")
            errors.extend(step_errors)

            # Parse the response
            if len(user_input_english.messages) < 3:
                raise ValueError("Translation response doesn't have enough messages")

            user_input_english_str = user_input_english.messages[2].content
            print(f"Translation response: {user_input_english_str}")

            user_input_english_json = safe_json_loads(user_input_english_str, "Translation step")
            user_input_english_text = user_input_english_json['translation']['text']
            user_input_original_language = user_input_english_json['translation']['original_language']

            return user_input_english_text, user_input_original_language, errors

        except Exception as e:
            errors.append(f"Error in normalized_user_input: {str(e)}")
            raise

    async def detect_timing(self, user_input: str) -> Tuple[Any, List[str]]:
        """Step 2: Detect timing in user input"""
        errors = []

        try:
            # Compile the prompt
            detect_timing_prompt = compile_prompt("detect_timing.prompt", {"user_input": user_input})

            # Run the agent
            detect_timing_response, step_errors = await self.run_agent_with_retry(detect_timing_prompt, "Timing Detection")
            errors.extend(step_errors)

            # Parse the response
            if len(detect_timing_response.messages) < 3:
                raise ValueError("Timing detection response doesn't have enough messages")

            detect_timing_response_str = detect_timing_response.messages[2].content
            print(f"Timing detection response: {detect_timing_response_str}")

            # Parse the JSON and return the Python object, not the string
            timing_json_obj = safe_json_loads(detect_timing_response_str, "Timing detection step")

            return timing_json_obj, errors

        except Exception as e:
            errors.append(f"Error in detect_timing: {str(e)}")
            raise

    async def cleaned_task(self, user_input: str) -> Tuple[str, List[str]]:
        """Step 3: Clean task (remove timing)"""
        errors = []

        try:
            # Compile the prompt
            cleaned_task_prompt = compile_prompt("remove_timing.prompt", {"user_input": user_input})

            # Run the agent
            cleaned_task_response, step_errors = await self.run_agent_with_retry(cleaned_task_prompt, "Task Cleaning")
            errors.extend(step_errors)

            # Parse the response
            if len(cleaned_task_response.messages) < 3:
                raise ValueError("Task cleaning response doesn't have enough messages")

            cleaned_task_response_str = cleaned_task_response.messages[2].content
            print(f"Task cleaning response: {cleaned_task_response_str}")

            return cleaned_task_response_str, errors

        except Exception as e:
            errors.append(f"Error in cleaned_task: {str(e)}")
            raise

    async def detect_apps(self, user_input: str) -> Tuple[Any, List[str]]:
        """Step 4: Detect apps in user input"""
        errors = []
        # Load JSON array from projectRoot/mcp-servers.json
        full_apps_data = json.load(open('mcp-servers.json'))

        # Extract only name, tags, and description properties from each app
        available_apps = [{
            "name": app['name'],
            "tags": app['tags'] if 'tags' in app else [],
            "description": app['description']
        } for app in full_apps_data]

        # Convert to JSON string for debugging
        available_apps_json = json.dumps(available_apps, indent=4)
        print('\navailable_apps_json: ' + available_apps_json)

        # Create a formatted string representation for the prompt
        # This will be a list of app details in a readable format
        app_descriptions = []
        for app in available_apps:
            tags_str = ", ".join(app['tags']) if app['tags'] else ""
            app_str = f"{app['name']} - {app['description']} [Tags: {tags_str}]"
            app_descriptions.append(app_str)

        # Join all app descriptions into a single string
        available_apps_str = "\n".join(app_descriptions)

        try:
            # Compile the prompt
            detect_apps_prompt = compile_prompt("detect_apps.prompt", {"available_apps": available_apps_str,
                                                                       "user_input": user_input})

            # Run the agent
            detect_apps_response, step_errors = await self.run_agent_with_retry(detect_apps_prompt, "App Detection")
            errors.extend(step_errors)

            # Parse the response
            if len(detect_apps_response.messages) < 3:
                raise ValueError("App detection response doesn't have enough messages")

            detect_apps_response_str = detect_apps_response.messages[2].content
            print(f"App detection response: {detect_apps_response_str}")

            # Parse the JSON and return the Python object, not the string
            detected_app_names = safe_json_loads(detect_apps_response_str, "App detection step")

            # Convert the detected app names to the required format with name, tags, and description
            detect_apps_json_obj = []
            for app_name in detected_app_names:
                # Find the corresponding app in our available_apps list
                matching_apps = [app for app in available_apps if app['name'] == app_name]

                if matching_apps:
                    # If we found a match, use its data
                    app_data = matching_apps[0]
                    detect_apps_json_obj.append({
                        "name": app_data['name'],
                        "tags": app_data['tags'],
                        "description": app_data['description']
                    })

            return detect_apps_json_obj, errors

        except Exception as e:
            errors.append(f"Error in detect_apps: {str(e)}")
            raise

    async def agent_prompt(self, cleaned_task_prompt: str, detect_apps_response: Any) -> Tuple[str, List[str]]:
        """Step 5: Generate Morrow prompt agent"""
        errors = []

        try:
            # Convert the Python object to a string for the prompt
            detect_apps_json_str = json.dumps(detect_apps_response, indent=4)

            # Compile the prompt
            agent_prompt = compile_prompt("task_to_agent.prompt",
                                        {"user_input": cleaned_task_prompt,
                                         "available_tools": detect_apps_json_str})

            # Run the agent
            agent_prompt_response, step_errors = await self.run_agent_with_retry(agent_prompt, "Morrow Prompt Generation")
            errors.extend(step_errors)

            # Parse the response
            if len(agent_prompt_response.messages) < 3:
                raise ValueError("Prompt generation response doesn't have enough messages")

            agent_prompt_response_str = agent_prompt_response.messages[2].content
            print(f"Prompt generation response: {agent_prompt_response_str}")

            return agent_prompt_response_str, errors

        except Exception as e:
            errors.append(f"Error in agent_prompt: {str(e)}")
            raise

    async def run_workflow(self, user_input: str) -> WorkflowResult:
        """Run the entire workflow"""
        result = WorkflowResult()
        result.original_user_input = user_input

        try:
            # Step 1: Translate
            print("\n=== Step 1: Translation ===")
            user_input_english_text, user_input_original_language, errors = await self.normalized_user_input(user_input)
            result.user_input_english_text = user_input_english_text
            result.original_user_input_language = user_input_original_language
            result.errors.extend(errors)

            # Step 2: Detect Timing
            print("\n=== Step 2: Timing Detection ===")
            timing_json, errors = await self.detect_timing(user_input_english_text)
            result.timing = timing_json
            result.errors.extend(errors)

            # Step 3: Clean Task
            print("\n=== Step 3: Task Cleaning ===")
            cleaned_task_prompt, errors = await self.cleaned_task(user_input_english_text)
            result.cleaned_task_prompt = cleaned_task_prompt
            result.errors.extend(errors)

            # Step 4: Detect Apps
            print("\n=== Step 4: App Detection ===")
            detect_apps_json, errors = await self.detect_apps(user_input_english_text)
            result.detect_apps_json = detect_apps_json
            result.errors.extend(errors)

            # Step 5: Morrow Prompt Agent
            print("\n=== Step 5: Morrow Prompt Generation ===")
            agent_prompt, errors = await self.agent_prompt(cleaned_task_prompt, detect_apps_json)
            result.agent_prompt = agent_prompt
            result.errors.extend(errors)

            return result

        except Exception as e:
            result.errors.append(f"Error in workflow: {str(e)}")
            return result


async def main():
    """Main function to run the workflow"""
    # Example user input
    user_input = ("Every end of day, go over my database and summarize all applications during that day, average amount, "
                 "group by company and save the summary to a file. in directory daily-summaries")

    # Create the workflow agent
    workflow_agent = WorkflowAgent()

    # Run the workflow
    result = await workflow_agent.run_workflow(user_input)

    # Print the result
    print("\nWorkflow Result:")
    print(result)


if __name__ == "__main__":
    asyncio.run(main())