# Morrow API

A B2C platform that enables everyone to run agentic tasks through a simple UI.

## Local Development

1. Install dependencies:
```bash
poetry install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run the development server:
```bash
poetry run uvicorn api:app --reload
```

## Docker Build

Build the Docker image:
```bash
docker build -t morrow-api .
```

Run the container locally:
```bash
docker run -p 8080:8080 --env-file .env morrow-api
```

## Google Cloud Run Deployment

1. Build and push the image to Google Container Registry:
```bash
# Set your project ID
export PROJECT_ID=your-project-id

# Build the image
docker build -t gcr.io/$PROJECT_ID/morrow-api .

# Push to GCR
docker push gcr.io/$PROJECT_ID/morrow-api
```

2. Deploy to Cloud Run:
```bash
gcloud run deploy morrow-api \
  --image gcr.io/$PROJECT_ID/morrow-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars="MONGODB_HOST=your-mongodb-host,MONGODB_USER=your-mongodb-user,MONGODB_PASSWORD=your-mongodb-password,ANTHROPIC_API_KEY=your-anthropic-api-key"
```

## API Documentation

Once the server is running, visit:
- Swagger UI: http://localhost:8080/docs
- ReDoc: http://localhost:8080/redoc

## System Architecture

```mermaid
flowchart LR
    %% === Websites Section ===
    subgraph Websites["Web Interfaces"]
        Website["<b>Website</b><br/>(www.morrow.so)"]
        Admin["<b>Admin Portal</b><br/>(admin.morrow.so)"]
    end

    %% === Backend Section ===
    subgraph Backend["<b>Backend Service</b>"]
        BE_Entities["<b>Entity Management</b><br/>users, workflows, apps"]
        BE_Scheduler["<b>Workflow Scheduler</b>"]
    end

    %% === Queues Section ===
    subgraph Queues["Queues"]
        CloudTasks["<b>Cloud Tasks</b>"]
        PubSub["<b>Pub/Sub</b>"]
    end

    %% === Agent Runner Section ===
    subgraph AgentRunner["<b>Agent Runner Service</b>"]
        AR_API["<b>API</b>"]
        AR_Orchestrator["<b>Workflow Orchestrator</b>"]
        AR_Local["<b>Local App Runner</b><br/>(stdio transport)"]
    end

    %% === Kubernetes MCP Cluster ===
    subgraph K8sMCPCluster["<b>K8s MCP Cluster</b>"]
        K8s_Pods["<b>Docker App Pods</b>"]
    end

    %% === Logger Section ===
    subgraph Logger["<b>Logger Service</b>"]
        Logger_Processor["<b>Log Processor</b>"]
    end

    %% === Storage ===
    MongoDB["<b>MongoDB</b>"]

    %% === Core Flow ===
    BE_Entities --> CloudTasks
    CloudTasks --> AR_API
    AR_API --> AR_Orchestrator
    AR_Orchestrator --> AR_Local
    AR_Orchestrator --> K8s_Pods

    %% === Data and Config Flows ===
    Backend <--> MongoDB
    AR_API -- "Fetch workflow config" --> Backend

    %% === Log Pipeline ===
    Backend -- "Logs" --> PubSub
    AgentRunner -- "Logs" --> PubSub
    PubSub --> Logger_Processor
    Logger_Processor --> MongoDB

    %% === Web Interfaces to Backend ===
    Website <--> Backend
    Admin <--> Backend

    %% === Styling Classes ===
    classDef website fill:#E8D7FF,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef backend fill:#D0E2FF,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef agentrunner fill:#D6F5D6,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef k8s fill:#FFD6D6,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef queue fill:#FFF9C4,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef logger fill:#C5F6FA,stroke:#666,stroke-width:1px,color:#000,font-weight:bold
    classDef storage fill:#ECECEC,stroke:#666,stroke-width:1px,color:#000,font-weight:bold

    %% === Assign Classes ===
    class Website,Admin website
    class BE_Entities,BE_Scheduler backend
    class AR_API,AR_Orchestrator,AR_Local agentrunner
    class K8s_Pods k8s
    class CloudTasks,PubSub queue
    class Logger_Processor logger
    class MongoDB storage
```

## Environment Variables

Required environment variables:
- `MONGODB_HOST`: MongoDB host
- `MONGODB_USER`: MongoDB username
- `MONGODB_PASSWORD`: MongoDB password
- `ANTHROPIC_API_KEY`: Anthropic API key for Claude
- `PORT`: Server port (defaults to 8080)

Optional environment variables:
- `MONGODB_DB_NAME`: MongoDB database name (defaults to "morrow")
- `AGNO_DEBUG`: Enable debug mode (defaults to false)

OAuth environment variables (required for OAuth features):
- `OAUTH_GOOGLE_CLIENT_ID`: Google OAuth client ID
- `OAUTH_GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `OAUTH_REDIRECT_URI`: OAuth callback URL (defaults to "https://api.morrow.ai/oauth/callback")