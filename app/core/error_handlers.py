import sys
import logging
import traceback
from typing import Any, Dict
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from rich.console import Console
from rich.traceback import install as install_rich_traceback
from better_exceptions import format_exception

from app.core.exceptions import MorrowBaseException

# Install rich traceback handler
install_rich_traceback(show_locals=True)
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def format_error_response(
    error_code: str,
    message: str,
    details: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Format error response consistently"""
    return {
        "error": {
            "code": error_code,
            "message": message,
            "details": details or {}
        }
    }

def setup_error_handlers(app: FastAPI) -> None:
    """Set up global exception handlers"""
    
    @app.exception_handler(MorrowBaseException)
    async def morrow_exception_handler(request: Request, exc: MorrowBaseException):
        """Handle all Morrow-specific exceptions"""
        # Log the error with rich formatting
        console.print("[red]Morrow Exception:[/red]", style="bold")
        console.print(f"[yellow]Error Code:[/yellow] {exc.error_code}")
        console.print(f"[yellow]Message:[/yellow] {exc.message}")
        console.print(f"[yellow]Details:[/yellow]", exc.details)
        
        # Log the full traceback for debugging
        logger.error(
            "Morrow exception occurred",
            extra={
                "error_code": exc.error_code,
                "status_code": exc.status_code,
                "details": exc.details,
                "path": request.url.path,
                "method": request.method,
            },
            exc_info=True
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=format_error_response(exc.error_code, exc.message, exc.details)
        )
    
    @app.exception_handler(Exception)
    async def unhandled_exception_handler(request: Request, exc: Exception):
        """Handle all unhandled exceptions"""
        # Get exception details
        exc_type, exc_value, exc_traceback = sys.exc_info()
        
        # Format traceback with better-exceptions
        formatted_tb = "".join(format_exception(exc_type, exc_value, exc_traceback))
        
        # Print rich formatted error
        console.print("[red]Unhandled Exception:[/red]", style="bold")
        console.print(formatted_tb)
        
        # Log the error
        logger.error(
            "Unhandled exception occurred",
            extra={
                "path": request.url.path,
                "method": request.method,
                "traceback": formatted_tb
            },
            exc_info=True
        )
        
        # In development, return detailed error info
        if app.debug:
            details = {
                "traceback": formatted_tb,
                "type": exc_type.__name__ if exc_type else None,
                "path": request.url.path,
                "method": request.method
            }
        else:
            details = None
        
        return JSONResponse(
            status_code=500,
            content=format_error_response(
                "INTERNAL_SERVER_ERROR",
                "An unexpected error occurred",
                details
            )
        ) 