from typing import Any, Dict, Optional
from fastapi import HTTPException, status

class MorrowBaseException(Exception):
    """Base exception for all Morrow exceptions"""
    def __init__(
        self,
        message: str,
        error_code: str = "INTERNAL_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

class WorkflowError(MorrowBaseException):
    """Base exception for workflow-related errors"""
    def __init__(
        self,
        message: str,
        error_code: str = "WORKFLOW_ERROR",
        status_code: int = status.HTTP_400_BAD_REQUEST,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, status_code, details)

class DatabaseError(MorrowBaseException):
    """Base exception for database-related errors"""
    def __init__(
        self,
        message: str,
        error_code: str = "DATABASE_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, status_code, details)

class ValidationError(MorrowBaseException):
    """Base exception for validation errors"""
    def __init__(
        self,
        message: str,
        error_code: str = "VALIDATION_ERROR",
        status_code: int = status.HTTP_422_UNPROCESSABLE_ENTITY,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, status_code, details)

class AuthenticationError(MorrowBaseException):
    """Base exception for authentication errors"""
    def __init__(
        self,
        message: str,
        error_code: str = "AUTHENTICATION_ERROR",
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, error_code, status_code, details)

class ExternalServiceError(MorrowBaseException):
    """Base exception for external service errors (e.g., Cloud Tasks, MongoDB)"""
    def __init__(
        self,
        message: str,
        service_name: str,
        error_code: str = "EXTERNAL_SERVICE_ERROR",
        status_code: int = status.HTTP_503_SERVICE_UNAVAILABLE,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        details["service_name"] = service_name
        super().__init__(message, error_code, status_code, details)

# Specific Workflow Exceptions
class WorkflowNotFoundError(WorkflowError):
    def __init__(self, workflow_id: str):
        super().__init__(
            message=f"Workflow with ID {workflow_id} not found",
            error_code="WORKFLOW_NOT_FOUND",
            status_code=status.HTTP_404_NOT_FOUND,
            details={"workflow_id": workflow_id}
        )

class WorkflowValidationError(WorkflowError):
    def __init__(self, message: str, validation_errors: Dict[str, Any]):
        super().__init__(
            message=message,
            error_code="WORKFLOW_VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"validation_errors": validation_errors}
        )

class WorkflowStateError(WorkflowError):
    def __init__(self, message: str, current_state: str, expected_state: str):
        super().__init__(
            message=message,
            error_code="WORKFLOW_STATE_ERROR",
            status_code=status.HTTP_409_CONFLICT,
            details={
                "current_state": current_state,
                "expected_state": expected_state
            }
        ) 