import json
import asyncio
import os
import time
import re
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List
from textwrap import dedent

from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.models.openai import OpenAIChat
from agno.run.response import RunResponse
from utils.json_utils import safe_json_loads
from pydantic import BaseModel

# Add import for prompt service dependency
from app.repositories.prompt_repository import PromptRepository
from app.services.prompt_service import PromptService
from app.database.mongodb import MongoDB

# Maximum number of retries for each step
MAX_RETRIES = 3
# Delay between retries in seconds
RETRY_DELAY = 2

class WorkflowState(BaseModel):
    original_user_input: str
    eligibility: Optional[Dict[str, Any]] = None
    user_input_english_text: Optional[str] = None
    original_user_input_language: Optional[str] = None
    timing: Optional[Dict[str, Any]] = None
    cleaned_task_prompt: Optional[str] = None
    suggested_apps: Optional[List[Dict[str, Any]]] = None
    user_confirmed_apps: Optional[List[Dict[str, Any]]] = None
    app_variables: Optional[Dict[str, str]] = None
    share_workflow: bool = False
    agent_prompt: Optional[str] = None
    errors: List[str] = []
    status: str = "pending"  # Can be "pending", "in_progress", "success", "failure"


class WorkflowResult:
    """Class to store the results of the workflow"""
    def __init__(self):
        self.original_user_input = ""
        self.original_user_input_language = ""
        self.user_input_english_text = ""
        self.eligibility = None  # Store as Python object, not string
        self.timing = None  # Store as Python object, not string
        self.cleaned_task_prompt = ""
        self.suggested_apps = None  # Store as Python object, not string
        self.agent_prompt = ""
        self.errors = []
        self.status = "success"  # Can be "success" or "failure"

    def to_dict(self) -> Dict[str, Any]:
        """Convert the result to a dictionary"""
        return {
            "original_user_input": self.original_user_input,
            "original_user_input_language": self.original_user_input_language,
            "user_input_english_text": self.user_input_english_text,
            "eligibility": self.eligibility,  # Will be serialized properly by json.dumps
            "timing": self.timing,  # Will be serialized properly by json.dumps
            "cleaned_task_prompt": self.cleaned_task_prompt,
            "suggested_apps": self.suggested_apps,  # Will be serialized properly by json.dumps
            "agent_prompt": self.agent_prompt,
            "errors": self.errors,
            "status": self.status
        }

    def __str__(self) -> str:
        """Convert the result to a string"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)  # Preserve non-ASCII characters


async def load_template(template_name: str) -> str:
    """Load a template from MongoDB by name (content property)"""
    db = MongoDB.get_db()
    repository = PromptRepository(db)
    service = PromptService(repository)
    return await service.get_prompt_content_by_name(template_name)


async def compile_prompt(template_name: str, vars: dict) -> str:
    """Compile a prompt template with variables, loading from MongoDB"""
    template = await load_template(template_name)
    for key, value in vars.items():
        template = template.replace(f"{{{key}}}", value)
    return template


class WorkflowAgent:
    """Class to handle the workflow of agents"""

    def __init__(self):
        """Initialize the workflow agent with available models"""
        self.models = [
            Claude(id="claude-3-7-sonnet-20250219"),
            Claude(id="claude-3-5-sonnet-20241022"),
        ]
        self.current_model_index = 0

    def get_next_model(self):
        """Get the next model to use as a fallback"""
        self.current_model_index = (self.current_model_index + 1) % len(self.models)
        return self.models[self.current_model_index]

    def reset_model(self):
        """Reset to the first model"""
        self.current_model_index = 0

    async def run_agent_with_retry(self, prompt: str, step_name: str) -> Tuple[RunResponse, List[str]]:
        """Run an agent with retries and model fallbacks"""
        errors = []
        retries = 0

        while retries < MAX_RETRIES:
            try:
                # Get the current model
                model = self.models[self.current_model_index]

                # Create the agent
                agent = Agent(
                    model=model,
                    description=dedent("""\
                    You are working AI Agent for Morrow, a B2C platform that enables everyone to run agentic tasks through a simple UI.
                    """),
                    markdown=False,
                )

                # Run the agent
                response = await agent.arun(prompt)

                # Debug: Print the response content
                print(f"\n{step_name} Response (attempt {retries + 1}):")
                print(f"Response messages: {len(response.messages)}")
                if len(response.messages) >= 3:
                    print(f"Content: {response.messages[2].content}")
                else:
                    print("Not enough messages in response")

                return response, errors

            except Exception as e:
                error_msg = f"Error in {step_name} (attempt {retries + 1}/{MAX_RETRIES}): {str(e)}"
                print(error_msg)
                errors.append(error_msg)

                # Try with a different model
                self.get_next_model()

                # Wait before retrying
                await asyncio.sleep(RETRY_DELAY)
                retries += 1

        # If we've exhausted all retries, raise an exception
        raise Exception(f"Failed to run {step_name} after {MAX_RETRIES} attempts: {errors}")

    async def normalized_user_input(self, user_input: str) -> Tuple[str, str, List[str]]:
        """Step 1: Translate user input to English"""
        errors = []

        try:
            # Compile the prompt
            translated_input_prompt = await compile_prompt("translate", {"user_input": user_input})
            print('\ntranslated_input: ' + translated_input_prompt)

            # Run the agent
            user_input_english, step_errors = await self.run_agent_with_retry(translated_input_prompt, "Translation")
            errors.extend(step_errors)

            # Parse the response
            if len(user_input_english.messages) < 3:
                raise ValueError("Translation response doesn't have enough messages")

            user_input_english_str = user_input_english.messages[2].content
            print(f"Translation response: {user_input_english_str}")

            user_input_english_json = safe_json_loads(user_input_english_str, "Translation step")
            user_input_english_text = user_input_english_json['translated_text']
            user_input_original_language = user_input_english_json['original_language']

            return user_input_english_text, user_input_original_language, errors

        except Exception as e:
            errors.append(f"Error in normalized_user_input: {str(e)}")
            raise

    async def detect_timing(self, user_input: str) -> Tuple[Any, List[str]]:
        """Step 2: Detect timing in user input"""
        errors = []

        try:
            # Compile the prompt
            detect_timing_prompt = await compile_prompt("detect_timing", {"user_input": user_input})

            # Run the agent
            detect_timing_response, step_errors = await self.run_agent_with_retry(detect_timing_prompt, "Timing Detection")
            errors.extend(step_errors)

            # Parse the response
            if len(detect_timing_response.messages) < 3:
                raise ValueError("Timing detection response doesn't have enough messages")

            detect_timing_response_str = detect_timing_response.messages[2].content
            print(f"Timing detection response: {detect_timing_response_str}")

            # Parse the JSON and return the Python object, not the string
            timing_json_obj = safe_json_loads(detect_timing_response_str, "Timing detection step")

            return timing_json_obj, errors

        except Exception as e:
            errors.append(f"Error in detect_timing: {str(e)}")
            raise

    async def cleaned_task(self, user_input: str) -> Tuple[str, List[str]]:
        """Step 3: Clean task (remove timing)"""
        errors = []

        try:
            # Compile the prompt
            cleaned_task_prompt = await compile_prompt("remove_timing", {"user_input": user_input})

            # Run the agent
            cleaned_task_response, step_errors = await self.run_agent_with_retry(cleaned_task_prompt, "Task Cleaning")
            errors.extend(step_errors)

            # Parse the response
            if len(cleaned_task_response.messages) < 3:
                raise ValueError("Task cleaning response doesn't have enough messages")

            cleaned_task_response_str = cleaned_task_response.messages[2].content
            print(f"Task cleaning response: {cleaned_task_response_str}")

            return cleaned_task_response_str, errors

        except Exception as e:
            errors.append(f"Error in cleaned_task: {str(e)}")
            raise

    async def suggest_apps(self, user_input: str, user_id: Optional[str] = None) -> Tuple[Any, List[str]]:
        """Step 4: Suggest apps in user input"""
        errors = []

        try:
            # Get app service from dependencies
            from app.core.dependencies import get_app_service
            from app.core.dependencies import get_app_repository
            from app.core.dependencies import get_app_credential_repository
            from app.core.dependencies import get_app_credential_service
            from app.database.mongodb import MongoDB

            # Get database connection
            db = MongoDB.get_db()

            # Create app repository and service
            app_repository = await get_app_repository(db)
            app_service = await get_app_service(app_repository)

            # Create app credential repository and service
            app_credential_repository = await get_app_credential_repository(db)
            app_credential_service = await get_app_credential_service(app_credential_repository, app_service)

            # Fetch all apps from the app service
            all_apps = await app_service.list_apps()

            # If no apps are available, return empty list
            if not all_apps:
                return [], errors

            # Create a simplified version for the prompt with just name, tags, and description
            app_descriptions = []
            for app in all_apps:
                tags_str = ", ".join(app.tags) if app.tags else ""
                app_str = f"{app.name} - {app.description} [Tags: {tags_str}]"
                app_descriptions.append(app_str)

            # Join all app descriptions into a single string
            available_apps_str = "\n".join(app_descriptions)

            # Compile the prompt
            suggest_apps_prompt = await compile_prompt("suggest_apps", {
                "available_apps": available_apps_str,
                "user_input": user_input
            })

            # Run the agent
            suggest_apps_response, step_errors = await self.run_agent_with_retry(suggest_apps_prompt, "App Suggestion")
            errors.extend(step_errors)

            # Parse the response
            if len(suggest_apps_response.messages) < 3:
                raise ValueError("App suggestion response doesn't have enough messages")

            suggest_apps_response_str = suggest_apps_response.messages[2].content
            print(f"App suggestion response: {suggest_apps_response_str}")

            # Parse the JSON and return the Python object, not the string
            suggested_app_names = safe_json_loads(suggest_apps_response_str, "App suggestion step")

            # If no apps were suggested, return empty list
            if not suggested_app_names:
                return [], errors

            # Build a lookup for all apps by lowercased name
            app_lookup = {app.name.lower(): app for app in all_apps}

            suggested_apps_data = []
            for suggested_name in suggested_app_names:
                app = app_lookup.get(suggested_name.lower())
                if not app:
                    continue  # skip if not found

                # Prepare auth variables (status will be updated by process_suggested_apps)
                auth_variables = []
                for auth_var in app.auth_variables:
                    auth_var_dict = {
                        "name": auth_var.name,
                        "type": auth_var.type,
                        "description": auth_var.description,
                        "required": auth_var.required
                    }
                    auth_variables.append(auth_var_dict)

                suggested_apps_data.append({
                    "id": app.id,
                    "name": app.name.lower(),
                    "description": app.description,
                    "instructions": app.instructions,
                    "auth_variables": auth_variables,
                    "oauth_provider": app.oauth_provider if app.oauth_provider is not None else None,
                    "auth_method": app.auth_method
                })

            return suggested_apps_data, errors

        except Exception as e:
            errors.append(f"Error in suggest_apps: {str(e)}")
            raise

    async def agent_prompt(self, cleaned_task_prompt: str, suggested_apps_response: Any) -> Tuple[str, List[str]]:
        """Step 5: Generate Morrow prompt agent"""
        errors = []

        try:
            # Convert the Python object to a string for the prompt
            suggested_apps_json_str = json.dumps(suggested_apps_response, indent=4)

            # Compile the prompt
            agent_prompt = await compile_prompt("task_to_agent",
                                        {"user_input": cleaned_task_prompt,
                                         "available_tools": suggested_apps_json_str})

            # Run the agent
            agent_prompt_response, step_errors = await self.run_agent_with_retry(agent_prompt, "Morrow Prompt Generation")
            errors.extend(step_errors)

            # Parse the response
            if len(agent_prompt_response.messages) < 3:
                raise ValueError("Morrow prompt generation response doesn't have enough messages")

            agent_prompt_response_str = agent_prompt_response.messages[2].content
            print(f"Morrow prompt generation response: {agent_prompt_response_str}")

            return agent_prompt_response_str, errors

        except Exception as e:
            errors.append(f"Error in agent_prompt: {str(e)}")
            raise

    async def eligibility_check(self, user_input: str) -> Tuple[Any, List[str]]:
        """Step 1: Check if the user input is eligible for processing"""
        errors = []

        try:
            # Compile the prompt
            eligibility_check_prompt = await compile_prompt("eligibility_check", {"user_input": user_input})
            # Run the agent
            eligibility_check_response, step_errors = await self.run_agent_with_retry(eligibility_check_prompt, "Eligibility Check")
            errors.extend(step_errors)

            # Parse the response
            if len(eligibility_check_response.messages) < 3:
                raise ValueError("Eligibility check response doesn't have enough messages")

            eligibility_check_response_str = eligibility_check_response.messages[2].content
            print(f"Eligibility check response: {eligibility_check_response_str}")

            # Parse the JSON and return the Python object, not the string
            eligibility_json_obj = safe_json_loads(eligibility_check_response_str, "Eligibility check step")

            return eligibility_json_obj, errors

        except Exception as e:
            errors.append(f"Error in eligibility_check: {str(e)}")
            raise