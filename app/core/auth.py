from fastapi import HTTPException, Security, Depends
from fastapi.security.api_key import APIKeyHeader
from app.database.mongodb import MongoDB
from datetime import datetime, UTC
import secrets
from bson import ObjectId

api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

async def get_api_key(api_key: str = Security(api_key_header)):
    db = MongoDB.get_db()
    api_key_doc = await db.api_keys.find_one({"key": api_key, "is_active": True})
    
    if not api_key_doc:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    
    # Update last used timestamp
    await db.api_keys.update_one(
        {"_id": api_key_doc["_id"]},
        {"$set": {"last_used_at": datetime.now(UTC)}}
    )
    return api_key_doc

async def get_admin_user(api_key_doc: dict = Depends(get_api_key)):
    db = MongoDB.get_db()
    user = await db.users.find_one({"_id": ObjectId(api_key_doc["user_id"])})
    if not user or "admin" not in user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Admin role required"
        )
    
    return user

def generate_api_key() -> str:
    """Generate a secure API key."""
    return secrets.token_urlsafe(32) 