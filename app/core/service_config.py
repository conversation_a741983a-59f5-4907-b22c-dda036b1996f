from fastapi import FastAP<PERSON>
from app.core.dependencies import (
    get_workflow_service,
    get_workflow_repository,
    get_workflow_agent,
    get_app_service,
    get_app_repository,
    get_workflow_run_service,
    get_workflow_run_repository,
    get_app_credential_service,
    get_app_credential_repository,
    get_oauth_service
)
from app.repositories.workflow_repository import WorkflowRepository
from app.repositories.app_repository import AppRepository
from app.repositories.workflow_run_repository import WorkflowRunRepository
from app.repositories.app_credential_repository import AppCredentialRepository
from app.services.workflow_service import WorkflowService
from app.services.app_service import AppService
from app.services.workflow_run_service import WorkflowRunService
from app.services.app_credential_service import AppCredentialService
from app.services.oauth_service import OAuthService
from app.core.workflow.agent import WorkflowAgent

def configure_services(app: FastAPI):
    """Configure services and dependencies for the application"""

    # Register dependencies
    app.dependency_overrides[get_workflow_repository] = get_workflow_repository
    app.dependency_overrides[get_workflow_agent] = get_workflow_agent
    app.dependency_overrides[get_workflow_service] = get_workflow_service
    app.dependency_overrides[get_app_repository] = get_app_repository
    app.dependency_overrides[get_app_service] = get_app_service
    app.dependency_overrides[get_workflow_run_repository] = get_workflow_run_repository
    app.dependency_overrides[get_workflow_run_service] = get_workflow_run_service
    app.dependency_overrides[get_app_credential_repository] = get_app_credential_repository
    app.dependency_overrides[get_app_credential_service] = get_app_credential_service
    app.dependency_overrides[get_oauth_service] = get_oauth_service
