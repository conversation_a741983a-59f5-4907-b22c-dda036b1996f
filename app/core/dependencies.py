from fastapi import Depends
from typing import Annotated

from app.database.mongodb import MongoDB
from app.repositories.workflow_repository import WorkflowRepository
from app.repositories.app_repository import AppRepository
from app.repositories.workflow_run_repository import WorkflowRunRepository
from app.repositories.blog_post_repository import BlogPostRepository
from app.repositories.prompt_repository import PromptRepository
from app.repositories.app_credential_repository import AppCredentialRepository
from app.services.workflow_service import WorkflowService
from app.services.app_service import AppService
from app.services.workflow_run_service import WorkflowRunService
from app.services.blog_post_service import BlogPostService
from app.services.storage_service import StorageService
from app.services.app_credential_service import AppCredentialService
from app.services.oauth_service import OAuthService
from app.core.workflow.agent import WorkflowAgent
from app.services.prompt_service import PromptService

async def get_db():
    """Get the database connection"""
    return MongoDB.get_db()

async def get_workflow_repository(db = Depends(get_db)):
    """Get the workflow repository"""
    return WorkflowRepository(db)

async def get_app_repository(db = Depends(get_db)):
    """Get the app repository"""
    return AppRepository(db)

async def get_workflow_run_repository(db = Depends(get_db)):
    """Get the workflow run repository"""
    return WorkflowRunRepository(db)

async def get_blog_post_repository(db = Depends(get_db)):
    """Get the blog post repository"""
    return BlogPostRepository(db)

async def get_prompt_repository(db = Depends(get_db)):
    """Get the prompt repository"""
    return PromptRepository(db)

async def get_app_credential_repository(db = Depends(get_db)):
    """Get the app credential repository"""
    return AppCredentialRepository(db)

async def get_storage_service():
    """Get the storage service"""
    return StorageService()

async def get_workflow_agent():
    """Get the workflow agent"""
    return WorkflowAgent()

async def get_app_service(
    repository: AppRepository = Depends(get_app_repository)
):
    """Get the app service"""
    return AppService(repository)

async def get_app_credential_service(
    repository: AppCredentialRepository = Depends(get_app_credential_repository),
    app_service: AppService = Depends(get_app_service)
) -> AppCredentialService:
    """Get the app credential service"""
    return AppCredentialService(repository, app_service)

async def get_workflow_service(
    repository: WorkflowRepository = Depends(get_workflow_repository),
    agent: WorkflowAgent = Depends(get_workflow_agent),
    app_service: AppService = Depends(get_app_service),
    app_credential_service: AppCredentialService = Depends(get_app_credential_service)
):
    """Get the workflow service"""
    return WorkflowService(repository, agent, app_service, app_credential_service)

async def get_workflow_run_service(
    repository: WorkflowRunRepository = Depends(get_workflow_run_repository)
) -> WorkflowRunService:
    """Get the workflow run service"""
    return WorkflowRunService(repository)

async def get_blog_post_service(
    repository: BlogPostRepository = Depends(get_blog_post_repository),
    storage_service: StorageService = Depends(get_storage_service)
) -> BlogPostService:
    """Get the blog post service"""
    return BlogPostService(repository)

async def get_prompt_service(
    repository: PromptRepository = Depends(get_prompt_repository)
) -> PromptService:
    """Get the prompt service"""
    return PromptService(repository)

async def get_oauth_service(
    app_service: AppService = Depends(get_app_service),
    app_credential_service: AppCredentialService = Depends(get_app_credential_service)
) -> OAuthService:
    """Get the OAuth service"""
    return OAuthService(app_service, app_credential_service)


# Type annotations for easier dependency injection
DBDependency = Annotated[MongoDB, Depends(get_db)]
WorkflowRepositoryDependency = Annotated[WorkflowRepository, Depends(get_workflow_repository)]
AppRepositoryDependency = Annotated[AppRepository, Depends(get_app_repository)]
WorkflowRunRepositoryDependency = Annotated[WorkflowRunRepository, Depends(get_workflow_run_repository)]
BlogPostRepositoryDependency = Annotated[BlogPostRepository, Depends(get_blog_post_repository)]
StorageServiceDependency = Annotated[StorageService, Depends(get_storage_service)]
WorkflowAgentDependency = Annotated[WorkflowAgent, Depends(get_workflow_agent)]
WorkflowServiceDependency = Annotated[WorkflowService, Depends(get_workflow_service)]
AppServiceDependency = Annotated[AppService, Depends(get_app_service)]
WorkflowRunServiceDependency = Annotated[WorkflowRunService, Depends(get_workflow_run_service)]
BlogPostServiceDependency = Annotated[BlogPostService, Depends(get_blog_post_service)]
PromptRepositoryDependency = Annotated[PromptRepository, Depends(get_prompt_repository)]
PromptServiceDependency = Annotated[PromptService, Depends(get_prompt_service)]
AppCredentialRepositoryDependency = Annotated[AppCredentialRepository, Depends(get_app_credential_repository)]
AppCredentialServiceDependency = Annotated[AppCredentialService, Depends(get_app_credential_service)]
OAuthServiceDependency = Annotated[OAuthService, Depends(get_oauth_service)]
