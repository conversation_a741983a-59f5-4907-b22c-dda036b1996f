from bson import ObjectId
from typing import Any, Annotated
from pydantic import BeforeValidator

def validate_object_id(v: Any) -> str:
    """Validate if the value is a valid ObjectId and convert to string"""
    if isinstance(v, ObjectId):
        return str(v)
    if isinstance(v, str):
        try:
            ObjectId(v)
            return v
        except Exception:
            raise ValueError("Invalid ObjectId format")
    raise ValueError("Invalid ObjectId")

# Define a type that validates and converts ObjectId
PyObjectId = Annotated[str, BeforeValidator(validate_object_id)]
