import logging
from typing import Type, TypeVar, List, Optional, Tuple, Dict, Any
from bson import ObjectId
from pydantic import BaseModel
from datetime import datetime, UTC

# Set up logging
logger = logging.getLogger(__name__)

# Generic type for the model
T = TypeVar('T', bound=BaseModel)

class Repository:
    """Generic repository for MongoDB operations"""
    
    def __init__(self, db, collection_name: str, model_class: Type[T]):
        self.db = db
        self.collection_name = collection_name
        self.collection = db[collection_name]
        self.model_class = model_class
    
    def _prepare_document(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare document for model creation with proper defaults"""
        # We should still maintain the prompt field for the domain model
        # but ensure original_user_input is set
        if "original_user_input" not in document and "prompt" in document:
            document["original_user_input"] = document["prompt"]
        
        # Conversely, if original_user_input exists but prompt doesn't, set prompt from original_user_input
        if "prompt" not in document and "original_user_input" in document:
            document["prompt"] = document["original_user_input"]
            
        # Ensure agent_prompt is a string, not None
        if "agent_prompt" in document and document["agent_prompt"] is None:
            document["agent_prompt"] = ""
            
        return document
    
    def _replace_empty_strings_with_none(self, obj):
        if isinstance(obj, dict):
            return {k: self._replace_empty_strings_with_none(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._replace_empty_strings_with_none(item) for item in obj]
        elif obj == "":
            return None
        else:
            return obj
    
    async def find_by_id(self, id: str) -> Optional[T]:
        """Find a document by ID"""
        try:
            result = await self.collection.find_one({"_id": ObjectId(id)})
            if result:
                result = self._prepare_document(result)
                return self.model_class(**result)
            return None
        except Exception as e:
            logger.error(f"Error finding document in {self.collection_name} by id {id}: {str(e)}")
            return None
    
    async def find_all(self, skip: int = 0, limit: int = 10, query: Dict[str, Any] = None, sort: Optional[List[Tuple[str, int]]] = None) -> Tuple[List[T], int]:
        """Find all documents with pagination, optional query, and optional sorting"""
        try:
            if query is None:
                query = {}
            total = await self.collection.count_documents(query)
            cursor = self.collection.find(query)
            if sort:
                cursor = cursor.sort(sort)
            cursor = cursor.skip(skip).limit(limit)
            documents = await cursor.to_list(length=None)
            prepared_documents = []
            for doc in documents:
                try:
                    prepared_doc = self._prepare_document(doc)
                    model_instance = self.model_class(**prepared_doc)
                    prepared_documents.append(model_instance)
                except Exception as e:
                    logger.error(f"Error creating model from document: {str(e)}")
                    logger.error(f"Document data: {doc}")
                    continue
            return prepared_documents, total
        except Exception as e:
            logger.error(f"Error finding documents in {self.collection_name}: {str(e)}")
            return [], 0
    
    async def create(self, model: T) -> Optional[str]:
        """Create a new document"""
        try:
            document = model.model_dump(by_alias=True)
            # Remove the id if it's present but None
            if "_id" in document and document["_id"] is None:
                del document["_id"]
            # Replace empty strings with None
            document = self._replace_empty_strings_with_none(document)
            result = await self.collection.insert_one(document)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating document in {self.collection_name}: {str(e)}")
            return None
    
    async def update(self, id: str, model: T, exclude_unset: bool = True) -> bool:
        """Update a document"""
        try:
            # Exclude unset fields to only update what changed
            document = model.model_dump(by_alias=True, exclude_unset=exclude_unset)
            
            # Remove _id from update data if present
            if "_id" in document:
                del document["_id"]
            # Replace empty strings with None
            document = self._replace_empty_strings_with_none(document)
            
            result = await self.collection.update_one(
                {"_id": ObjectId(id)},
                {"$set": document}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating document in {self.collection_name} with id {id}: {str(e)}")
            return False
    
    async def delete(self, id: str) -> bool:
        """Delete a document"""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting document in {self.collection_name} with id {id}: {str(e)}")
            return False
