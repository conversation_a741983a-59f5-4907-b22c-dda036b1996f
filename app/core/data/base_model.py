from datetime import datetime, UTC
from pydantic import BaseModel, Field, ConfigDict
from bson import ObjectId
from typing import Dict, Any

class BaseDBModel(BaseModel):
    """Base model for all database models with common fields and behaviors"""
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )
    
    def update_modified_at(self):
        """Update the updated_at timestamp"""
        self.updated_at = datetime.now(UTC)
