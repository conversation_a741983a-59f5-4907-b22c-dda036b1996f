from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache

class CoreSettings(BaseSettings):
    """Core application settings"""
    # API Keys
    anthropic_api_key: str
    agno_debug: bool = False
    use_pubsub: bool = False

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=False,
        extra="ignore"  # Changed from forbid to ignore to allow other env vars
    )

@lru_cache()
def get_core_settings() -> CoreSettings:
    """Get cached instance of core settings"""
    return CoreSettings() 