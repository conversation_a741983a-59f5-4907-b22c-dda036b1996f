"""
OAuth settings configuration.
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Dict
from functools import lru_cache

class OAuthSettings(BaseSettings):
    """OAuth specific settings"""

    # Google OAuth settings
    google_client_id: str
    google_client_secret: str
    redirect_uri: str = "http://localhost:8080/v1/oauth/callback"

    microsoft_client_id: str
    microsoft_client_secret: str
    # Frontend URL for redirects after OAuth
    frontend_url: str = "http://localhost:3001"

    # Additional provider settings can be added here

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=False,
        env_prefix="OAUTH_",
        extra="ignore"  # Allow other env vars
    )

@lru_cache()
def get_oauth_settings() -> OAuthSettings:
    """Get cached instance of OAuth settings"""
    return OAuthSettings()
