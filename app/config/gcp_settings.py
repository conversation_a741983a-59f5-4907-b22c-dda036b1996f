"""
Google Cloud Platform settings configuration.
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional
from functools import lru_cache

class GCPSettings(BaseSettings):
    """Google Cloud Platform specific settings"""
    
    # Project settings
    project_id: str
    location: str = "us-central1"  # Default to us-central1
    
    # Cloud Tasks settings
    tasks_queue: str
    agent_runner_service_endpoint: str  # e.g., "https://agent-runner-service.run.app"
    
    # Authentication settings
    google_application_credentials: Optional[str] = None

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=False,
        env_prefix="GCP_",
        extra="ignore"  # Allow other env vars
    )

@lru_cache()
def get_gcp_settings() -> GCPSettings:
    """Get cached instance of GCP settings"""
    return GCPSettings() 