from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache
from typing import Optional

class MongoDBSettings(BaseSettings):
    """MongoDB database settings"""
    # Connection settings
    host: str
    user: str
    password: str
    db_name: str = "morrow"
    
    # Performance settings
    min_pool_size: int = 10
    max_pool_size: int = 100
    timeout_ms: int = 30000
    auth_source: str = "admin"
    
    @property
    def mongodb_url(self) -> str:
        """Construct the MongoDB connection URL from components."""
        return f"mongodb+srv://{self.user}:{self.password}@{self.host}/?retryWrites=true&w=majority&appName=Cluster0"
    
    @property
    def mongodb_db_name(self) -> str:
        return self.db_name

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=False,
        env_prefix="MONGODB_",
        extra="ignore"  # Allow other env vars
    )

@lru_cache()
def get_mongodb_settings() -> MongoDBSettings:
    """Get cached instance of MongoDB settings"""
    return MongoDBSettings() 