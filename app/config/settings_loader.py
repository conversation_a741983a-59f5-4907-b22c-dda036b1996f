"""
Centralized settings management for the application.
Ensures settings are loaded in the correct order and only once.
"""
from functools import lru_cache
from typing import Dict, Any
from dotenv import load_dotenv

from .core_settings import CoreSettings
from .gcp_settings import GCPSettings
from .mongodb_settings import MongoDBSettings
from .oauth_settings import OAuthSettings

@lru_cache()
def initialize_settings() -> Dict[str, Any]:
    """
    Initialize all application settings.
    This should be called before any other application initialization.
    Returns a dictionary containing all settings instances.
    """
    # First, load environment variables
    load_dotenv()

    # Then initialize all settings
    return {
        "core": CoreSettings(),
        "mongodb": MongoDBSettings(),
        "gcp": GCPSettings(),
        "oauth": OAuthSettings()
    }

def get_settings() -> Dict[str, Any]:
    """Get all application settings"""
    return initialize_settings()

def get_core_settings() -> CoreSettings:
    """Get core application settings"""
    return get_settings()["core"]

def get_mongodb_settings() -> MongoDBSettings:
    """Get MongoDB settings"""
    return get_settings()["mongodb"]

def get_gcp_settings() -> GCPSettings:
    """Get GCP settings"""
    return get_settings()["gcp"]

def get_oauth_settings() -> OAuthSettings:
    """Get OAuth settings"""
    return get_settings()["oauth"]

def get_all_settings() -> Dict[str, Any]:
    """
    Get all application settings as a dictionary.
    Each settings module is namespaced under its own key.
    """
    return {
        "core": get_core_settings().model_dump(),
        "gcp": get_gcp_settings().model_dump(),
        "mongodb": get_mongodb_settings().model_dump(),
        "oauth": get_oauth_settings().model_dump()
    }