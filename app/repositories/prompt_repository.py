from app.core.data import Repository
from app.models.prompt_domain import Prompt
from typing import Optional

class PromptRepository(Repository):
    def __init__(self, db):
        super().__init__(db, "prompts", Prompt)

    async def find_by_name(self, name: str) -> Optional[Prompt]:
        result = await self.collection.find_one({"name": name})
        if result:
            return self.model_class(**result)
        return None 