import logging
from bson import ObjectId
from app.core.data import Repository
from app.models.app_domain import App
from typing import Optional, List, Tuple

logger = logging.getLogger(__name__)

class AppRepository(Repository):
    """Repository for app operations"""
    
    def __init__(self, db):
        super().__init__(db, "apps", App)
    
    async def find_by_name(self, name: str) -> Optional[App]:
        """Find an app by name"""
        try:
            result = await self.collection.find_one({"name": name})
            if result:
                return self.model_class(**result)
            return None
        except Exception as e:
            logger.error(f"Error finding app by name {name}: {str(e)}")
            return None 