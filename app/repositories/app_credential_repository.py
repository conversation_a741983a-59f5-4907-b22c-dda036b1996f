import logging
from bson import ObjectId
from app.core.data import Repository
from app.models.app_credential_domain import AppCredential
from typing import Optional, List, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class AppCredentialRepository(Repository):
    """Repository for app credential operations"""
    
    def __init__(self, db):
        super().__init__(db, "app_credentials", AppCredential)
    
    async def find_by_user_id(self, user_id: str, skip: int = 0, limit: int = 10) -> <PERSON>ple[List[AppCredential], int]:
        """Find app credentials by user ID"""
        try:
            return await self.find_all(skip, limit, {"user_id": user_id}, [("created_at", -1)])
        except Exception as e:
            logger.error(f"Error finding app credentials by user ID {user_id}: {str(e)}")
            return [], 0
    
    async def find_by_app_id(self, app_id: str, skip: int = 0, limit: int = 10) -> <PERSON><PERSON>[List[AppCredential], int]:
        """Find app credentials by app ID"""
        try:
            return await self.find_all(skip, limit, {"app_id": app_id}, [("created_at", -1)])
        except Exception as e:
            logger.error(f"Error finding app credentials by app ID {app_id}: {str(e)}")
            return [], 0
    
    async def find_by_user_and_app(self, user_id: str, app_id: str) -> Optional[AppCredential]:
        """Find app credentials by user ID and app ID"""
        try:
            result = await self.collection.find_one({"user_id": user_id, "app_id": app_id})
            if result:
                return self.model_class(**result)
            return None
        except Exception as e:
            logger.error(f"Error finding app credentials by user ID {user_id} and app ID {app_id}: {str(e)}")
            return None
    
    async def find_by_workflow_id(self, workflow_id: str, skip: int = 0, limit: int = 10) -> Tuple[List[AppCredential], int]:
        """Find app credentials by workflow ID"""
        try:
            return await self.find_all(skip, limit, {"workflow_ids": workflow_id}, [("created_at", -1)])
        except Exception as e:
            logger.error(f"Error finding app credentials by workflow ID {workflow_id}: {str(e)}")
            return [], 0
    
    async def add_workflow_id(self, credential_id: str, workflow_id: str) -> bool:
        """Add a workflow ID to app credentials"""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(credential_id)},
                {"$addToSet": {"workflow_ids": workflow_id}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error adding workflow ID {workflow_id} to app credentials {credential_id}: {str(e)}")
            return False
    
    async def remove_workflow_id(self, credential_id: str, workflow_id: str) -> bool:
        """Remove a workflow ID from app credentials"""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(credential_id)},
                {"$pull": {"workflow_ids": workflow_id}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error removing workflow ID {workflow_id} from app credentials {credential_id}: {str(e)}")
            return False
