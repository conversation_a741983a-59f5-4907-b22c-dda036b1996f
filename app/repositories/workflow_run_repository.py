import logging
from bson import ObjectId
from datetime import datetime, UTC
from app.core.data import Repository
from app.models.workflow_run import WorkflowRun, WorkflowStatus
from typing import Optional, List, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class WorkflowRunRepository(Repository):
    """Repository for workflow run operations"""
    
    def __init__(self, db):
        super().__init__(db, "workflow_runs", WorkflowRun)
        self.logs_collection = db["workflow_logs"]
    
    async def find_all(self, skip: int = 0, limit: int = 10, query: Dict[str, Any] = None, sort: Optional[List[Tuple[str, int]]] = None) -> Tuple[List[WorkflowRun], int]:
        if sort is None:
            sort = [("created_at", -1)]
        return await super().find_all(skip, limit, query, sort)
    
    async def find_by_workflow_id(self, workflow_id: str, skip: int = 0, limit: int = 10) -> <PERSON><PERSON>[List[WorkflowRun], int]:
        return await self.find_all(skip, limit, {"workflow_id": workflow_id}, [("created_at", -1)])
    
    async def find_by_status(self, status: WorkflowStatus, skip: int = 0, limit: int = 10) -> Tuple[List[WorkflowRun], int]:
        return await self.find_all(skip, limit, {"status": status}, [("created_at", -1)])
    
    async def update_status(self, run_id: str, status: WorkflowStatus, error_message: Optional[str] = None) -> bool:
        """Update workflow run status and optionally set error message"""
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.now(UTC)
            }
            
            if status == WorkflowStatus.RUNNING:
                update_data["started_at"] = datetime.now(UTC)
            elif status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED]:
                update_data["completed_at"] = datetime.now(UTC)
            
            if error_message and status == WorkflowStatus.FAILED:
                update_data["error_message"] = error_message
            
            result = await self.collection.update_one(
                {"_id": ObjectId(run_id)},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating workflow run status: {str(e)}")
            return False 

    async def get_workflow_logs(self, workflow_run_id: str, skip: int = 0, limit: int = 100) -> Tuple[List[Dict[str, Any]], int]:
        """Get logs for a specific workflow run"""
        try:
            # Get total count
            total = await self.logs_collection.count_documents({"workflow_run_id": workflow_run_id})
            
            # Get logs ordered by timestamp, excluding _id field
            cursor = self.logs_collection.find(
                {"workflow_run_id": workflow_run_id},
                {"_id": 0}  # Exclude _id field
            ).sort("timestamp", 1).skip(skip).limit(limit)
            
            logs = await cursor.to_list(length=limit)
            return logs, total
        except Exception as e:
            logger.error(f"Error fetching workflow logs: {str(e)}")
            raise 