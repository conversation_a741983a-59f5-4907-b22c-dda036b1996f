import logging
from bson import ObjectId
from app.core.data import Repository
from app.models.blog_post_domain import BlogPost
from typing import Optional, List, Tuple

logger = logging.getLogger(__name__)

class BlogPostRepository(Repository):
    """Repository for blog post operations"""
    
    def __init__(self, db):
        super().__init__(db, "blog_posts", BlogPost)
    
    async def find_by_title(self, title: str) -> Optional[BlogPost]:
        """Find a blog post by title"""
        try:
            result = await self.collection.find_one({"title": title})
            if result:
                return self.model_class(**result)
            return None
        except Exception as e:
            logger.error(f"Error finding blog post by title {title}: {str(e)}")
            return None
    
    async def find_by_slug(self, slug: str, locale: str = "en") -> Optional[BlogPost]:
        """Find a blog post by slug and locale"""
        try:
            result = await self.collection.find_one({f"slugs.{locale}": slug})
            if result:
                return self.model_class(**result)
            return None
        except Exception as e:
            logger.error(f"Error finding blog post by slug {slug} and locale {locale}: {str(e)}")
            return None 