import logging
from bson import ObjectId
from datetime import datetime, UTC
from app.core.data import Repository
from app.models.workflow_domain import Workflow
from typing import Optional, List, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class WorkflowRepository(Repository):
    """Repository for workflow operations"""
    
    def __init__(self, db):
        super().__init__(db, "workflows", Workflow)
    
    async def find_all(self, skip: int = 0, limit: int = 10, query: Dict[str, Any] = None, sort: Optional[List[Tuple[str, int]]] = None) -> Tuple[List[Workflow], int]:
        if sort is None:
            sort = [("created_at", -1)]
        return await super().find_all(skip, limit, query, sort)

    async def find_by_status(self, status: str, skip: int = 0, limit: int = 10) -> Tuple[List[Workflow], int]:
        return await self.find_all(skip, limit, {"status": status}, [("created_at", -1)])
    
    async def update_status(self, workflow_id: str, status: str) -> bool:
        """Update workflow status"""
        try:
            result = await self.collection.update_one(
                {"_id": ObjectId(workflow_id)},
                {"$set": {"status": status, "updated_at": datetime.now(UTC)}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating workflow status: {str(e)}")
            return False
