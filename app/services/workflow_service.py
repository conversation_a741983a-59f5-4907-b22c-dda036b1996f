import logging
from typing import Op<PERSON>, <PERSON>, Tuple, Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception

from app.repositories.workflow_repository import WorkflowRepository
from app.core.workflow.agent import WorkflowAgent
from app.services.app_service import AppService
from app.services.app_credential_service import AppCredentialService
from app.models.workflow_domain import Workflow
from app.models.workflow_dto import (
    WorkflowCreateDTO,
    WorkflowUpdateDTO,
    WorkflowConfirmDTO,
    WorkflowResponseDTO,
    APIResponseDTO,
    WorkflowStartResponseDTO
)
from app.models.app import AuthMethod

logger = logging.getLogger(__name__)

class WorkflowService:
    """Service for workflow business logic"""

    def __init__(
        self,
        repository: WorkflowRepository,
        agent: WorkflowAgent,
        app_service: AppService,
        app_credential_service: AppCredentialService
    ):
        self.repository = repository
        self.agent = agent
        self.app_service = app_service
        self.app_credential_service = app_credential_service

    def _to_response_dto(self, workflow: Workflow) -> WorkflowResponseDTO:
        """Convert a workflow domain model to a response DTO"""
        try:
            data = workflow.model_dump()

            # Ensure id is correctly handled
            if "_id" in data:
                data["id"] = data.pop("_id")

            # Ensure all required fields exist with defaults
            if "timing" not in data or data["timing"] is None:
                data["timing"] = {}
            if "suggested_apps" not in data or data["suggested_apps"] is None:
                data["suggested_apps"] = []
            if "errors" not in data:
                data["errors"] = []
            if "original_user_input" not in data or data["original_user_input"] is None:
                data["original_user_input"] = ""
            if "agent_prompt" in data and data["agent_prompt"] is None:
                data["agent_prompt"] = ""

            # Create and return the DTO
            return WorkflowResponseDTO(**data)
        except Exception as e:
            # Log the error and raise for proper handling
            logger.error(f"Error converting workflow to DTO: {str(e)}")
            logger.error(f"Workflow data: {workflow.model_dump()}")
            raise

    async def _process_suggested_apps(self, suggested_apps: List[Dict[str, Any]], user_id: str) -> List[Dict[str, Any]]:
        """Update suggested_apps with current credential status for the user."""
        from app.core.dependencies import get_app_credential_repository
        from app.database.mongodb import MongoDB
        db = MongoDB.get_db()
        app_credential_repository = await get_app_credential_repository(db)
        processed = []
        for app in suggested_apps:
            app_id = app.get("id")
            has_credentials = False
            if user_id and app_id:
                existing_credential = await app_credential_repository.find_by_user_and_app(user_id, app_id)
                has_credentials = existing_credential is not None

            app_copy = dict(app)
            app_copy["connection_status"] = "connected" if has_credentials else "not_connected"
            processed.append(app_copy)
            
        return processed

    async def get_workflow_by_id(self, workflow_id: str, user_id: Optional[str] = None) -> Optional[WorkflowStartResponseDTO]:
        """Get a workflow by ID and return up-to-date suggested_apps status"""
        try:
            workflow = await self.repository.find_by_id(workflow_id)
            if not workflow:
                return None
            suggested_apps = getattr(workflow, "suggested_apps", [])
            if user_id and suggested_apps:
                suggested_apps = await self._process_suggested_apps(suggested_apps, user_id)
            return WorkflowStartResponseDTO(
                status=workflow.status,
                workflow_id=str(workflow.id),
                timing=workflow.timing or {},
                suggested_apps=suggested_apps or []
            )
        except Exception as e:
            logger.error(f"Error getting workflow by ID: {str(e)}")
            return None

    async def get_workflow_entity_by_id(self, workflow_id: str) -> Optional[Workflow]:
        """Get a workflow by ID"""
        try:
            workflow = await self.repository.find_by_id(workflow_id)
            return workflow
        except Exception as e:
            logger.error(f"Error getting workflow by ID: {str(e)}")
            return None

    async def list_workflows(self, skip: int = 0, limit: int = 10) -> Tuple[List[WorkflowResponseDTO], int]:
        """List workflows with pagination"""
        try:
            workflows, total = await self.repository.find_all(skip, limit)
            result_workflows = []

            for workflow in workflows:
                try:
                    dto = self._to_response_dto(workflow)
                    result_workflows.append(dto)
                except Exception as e:
                    logger.error(f"Error converting workflow to DTO: {str(e)}")
                    # Skip this workflow and continue
                    continue

            return result_workflows, total
        except Exception as e:
            logger.error(f"Error listing workflows: {str(e)}")
            return [], 0

    async def create_workflow(self, dto: WorkflowCreateDTO, user_id: str) -> WorkflowStartResponseDTO:
        """Create a new workflow"""
        try:
            # Create a new workflow with initial data
            workflow = Workflow(
                prompt=dto.prompt,
                original_user_input=dto.prompt,
                status="setup",
                user_id=user_id
            )

            # Check eligibility
            eligibility_json, errors = await self.agent.eligibility_check(dto.prompt)
            workflow.eligibility = eligibility_json
            workflow.errors.extend(errors)
            # check number of actions on workflow.eligibility
            # if number of actions larger than 5, return error
            if len(eligibility_json.get('actions', [])) >= 5:
                workflow.status = "failed"
                workflow_id = await self.repository.create(workflow)
                return WorkflowStartResponseDTO(
                    status="error",
                    workflow_id=str(workflow_id),
                    timing={},
                    suggested_apps=[]
                )

            # Process the workflow using the agent
            workflow = await self._process_workflow(workflow)

            # Save the workflow
            workflow_id = await self.repository.create(workflow)
            if not workflow_id:
                raise HTTPException(status_code=500, detail="Failed to create workflow")

            # Return API response
            return WorkflowStartResponseDTO(
                status="success",
                workflow_id=str(workflow_id),
                timing=workflow.timing or {},
                suggested_apps=workflow.suggested_apps or []
            )
        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def update_workflow(self, workflow_id: str, dto: WorkflowUpdateDTO) -> WorkflowResponseDTO:
        """Update a workflow"""
        try:
            # Get existing workflow
            workflow = await self.repository.find_by_id(workflow_id)
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow not found")

            # Update fields from DTO
            update_data = dto.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(workflow, key, value)

                # If prompt is updated, also update original_user_input for consistency
                if key == "prompt" and value:
                    workflow.original_user_input = value

            # Update timestamp
            workflow.update_modified_at()

            # Save changes
            success = await self.repository.update(workflow_id, workflow)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to update workflow")

            # Return updated workflow
            return self._to_response_dto(workflow)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating workflow: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_workflow(self, workflow_id: str) -> bool:
        """Delete a workflow"""
        try:
            return await self.repository.delete(workflow_id)
        except Exception as e:
            logger.error(f"Error deleting workflow: {str(e)}")
            return False

    async def confirm_workflow(self, modifications: WorkflowConfirmDTO) -> APIResponseDTO:
        """Confirm and finalize a workflow with app configurations."""
        workflow = await self._get_and_validate_workflow_and_apps_for_confirmation(modifications.workflow_id, modifications.apps)

        # Validate all app credentials before making changes
        validation_errors = await self._validate_all_app_credentials(modifications.apps)
        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail={"message": "Invalid app credentials", "errors": validation_errors}
            )

        # Update workflow with confirmed apps and finalize
        await self._update_workflow_with_confirmed_apps(workflow, modifications)

        return APIResponseDTO(
            workflow_id=str(workflow.id),
            message="Workflow confirmed successfully"
        )

    async def _get_and_validate_workflow_and_apps_for_confirmation(self, workflow_id: str, apps: List[Any]) -> Workflow:
        workflow = await self.get_workflow_entity_by_id(workflow_id)
        if not workflow:
            raise HTTPException(status_code=404, detail="Workflow not found")
        if workflow.status != "setup":
            raise HTTPException(
                status_code=400,
                detail=f"Cannot confirm workflow in {workflow.status} status"
            )
        if not apps:
            raise HTTPException(
                status_code=400,
                detail="At least one app must be provided for confirmation"
            )
        return workflow

    async def _validate_all_app_credentials(self, apps: List[Any]) -> List[str]:
        errors = []
        for app_cred in apps:
            try:
                await self.app_service.validate_app_credentials(app_cred.app_id, app_cred)
            except HTTPException as e:
                errors.append(f"App {app_cred.app_id}: {e.detail}")
        return errors

    async def _update_workflow_with_confirmed_apps(self, workflow: Workflow, modifications: WorkflowConfirmDTO) -> None:
        # 1. Store only app IDs in the workflow.apps field
        app_ids = [app_cred.app_id for app_cred in modifications.apps]
        workflow.apps = app_ids

        # 2. Run finalize workflow with only app IDs (no credentials)
        workflow = await self._finalize_workflow(workflow)

        # 3. Save app credentials or update workflow_ids for OAuth apps
        for app_cred in modifications.apps:
            await self._handle_app_credential(workflow, app_cred)

        # 4. Update workflow status and save to database
        workflow.sharing_enabled = getattr(modifications, "sharing_enabled", False)
        workflow.status = "ready"
        workflow.update_modified_at()

        success = await self.repository.update(workflow.id, workflow)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to update workflow"
            )

    async def _handle_app_credential(self, workflow: Workflow, app_cred: Any) -> None:
        app = await self.app_service.get_app_by_id(app_cred.app_id)
        if app.auth_method == AuthMethod.OAUTH:
            await self._associate_workflow_with_oauth_credential(workflow, app_cred)
        else:
            await self._save_or_associate_non_oauth_credential(workflow, app_cred)

    async def _associate_workflow_with_oauth_credential(self, workflow: Workflow, app_cred: Any) -> None:
        repo = self.app_credential_service.repository
        existing_credential = await repo.find_by_user_and_app(workflow.user_id, app_cred.app_id)
        workflow_id_str = str(workflow.id)
        if not existing_credential:
            raise HTTPException(
                status_code=404,
                detail=f"No app credential found for OAuth app {app_cred.app_id}"
            )
        if workflow_id_str not in getattr(existing_credential, "workflow_ids", []):
            if hasattr(existing_credential, "workflow_ids"):
                existing_credential.workflow_ids.append(workflow_id_str)
            else:
                existing_credential.workflow_ids = [workflow_id_str]
            await repo.update(str(existing_credential.id), existing_credential)

    async def _save_or_associate_non_oauth_credential(self, workflow: Workflow, app_cred: Any) -> None:
        repo = self.app_credential_service.repository
        workflow_id_str = str(workflow.id)
        has_values = bool(app_cred.variables and any(var.value for var in app_cred.variables))
        if has_values:
            # Create new app credential and associate with workflow
            values = {var.name: var.value for var in app_cred.variables}
            credential, validation_errors = await self.app_credential_service.save_app_credential(
                app_id=app_cred.app_id,
                user_id=workflow.user_id,
                values=values,
                workflow_id=workflow_id_str
            )
            if validation_errors:
                for error in validation_errors:
                    logger.warning(f"Credential validation error for app {app_cred.app_id}: {error}")
        else:
            # Associate workflow with existing app credential
            existing_credential = await repo.find_by_user_and_app(workflow.user_id, app_cred.app_id)
            if not existing_credential:
                raise HTTPException(
                    status_code=404,
                    detail=f"No existing app credential found for app {app_cred.app_id} to associate with workflow"
                )
            if workflow_id_str not in getattr(existing_credential, "workflow_ids", []):
                if hasattr(existing_credential, "workflow_ids"):
                    existing_credential.workflow_ids.append(workflow_id_str)
                else:
                    existing_credential.workflow_ids = [workflow_id_str]
                await repo.update(str(existing_credential.id), existing_credential)

    async def _process_workflow(self, workflow: Workflow) -> Workflow:
        """Process a workflow using the agent"""
        try:
            # Step 1: Translation
            user_input_english_text, user_input_original_language, errors = await self.agent.normalized_user_input(workflow.original_user_input)
            workflow.user_input_english_text = user_input_english_text
            workflow.original_user_input_language = user_input_original_language
            workflow.errors.extend(errors)

            # Step 2: Detect Timing
            timing_json, errors = await self.agent.detect_timing(user_input_english_text)
            workflow.timing = timing_json
            workflow.errors.extend(errors)

            # Step 3: Clean Task
            cleaned_task_prompt, errors = await self.agent.cleaned_task(user_input_english_text)
            workflow.cleaned_task_prompt = cleaned_task_prompt
            workflow.errors.extend(errors)

            # Step 4: Suggest Apps
            suggested_apps_data, errors = await self.agent.suggest_apps(user_input_english_text, workflow.user_id)
            # Always process suggested_apps for up-to-date status
            if workflow.user_id and suggested_apps_data:
                suggested_apps_data = await self._process_suggested_apps(suggested_apps_data, workflow.user_id)
            workflow.suggested_apps = suggested_apps_data
            workflow.errors.extend(errors)

            return workflow
        except Exception as e:
            error_msg = f"Error processing workflow: {str(e)}"
            logger.error(error_msg)
            workflow.errors.append(error_msg)
            workflow.status = "failed"
            return workflow

    async def _finalize_workflow(self, workflow: Workflow) -> Workflow:
        """
        Finalize a workflow by generating the agent prompt.

        This method only uses app IDs without credentials, as credentials
        are stored separately in the app_credentials collection.
        """
        try:
            # Get app IDs from workflow
            app_ids = workflow.apps if workflow.apps else []

            # Get app details for each app ID
            apps_to_use = []
            for app_id in app_ids:
                app = await self.app_service.get_app_by_id(app_id)
                if app:
                    # Add app with empty variables (credentials handled separately)
                    apps_to_use.append({
                        "name": app.name,
                        "description": app.description
                    })

            # Generate agent prompt with app information (no credentials)
            agent_prompt, errors = await self.agent.agent_prompt(workflow.cleaned_task_prompt, apps_to_use)
            workflow.prompt = agent_prompt
            workflow.errors.extend(errors)

            return workflow
        except Exception as e:
            error_msg = f"Error finalizing workflow: {str(e)}"
            logger.error(error_msg)
            workflow.errors.append(error_msg)
            workflow.status = "failed"
            return workflow
