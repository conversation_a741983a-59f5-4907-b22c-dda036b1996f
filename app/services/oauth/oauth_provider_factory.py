from typing import Dict, Type
from app.services.oauth.oauth_provider_interface import OAuthProviderInterface
from app.services.oauth.providers.gmail_oauth_provider import GmailOAuthProvider
from app.services.oauth.providers.google_drive_oauth_provider import GoogleDriveOAuthProvider
from app.services.oauth.providers.google_calendar_oauth_provider import GoogleCalendarOAuthProvider
from app.services.oauth.providers.outlook_oauth_provider import OutlookOAuthProvider
from app.services.oauth.providers.onedrive_oauth_provider import OneDriveOAuthProvider
from app.config.settings_loader import get_oauth_settings
import logging

logger = logging.getLogger(__name__)


class OAuthProviderFactory:
    """Factory for creating OAuth provider instances"""
    
    # Map app names to their OAuth provider classes
    _PROVIDER_MAP: Dict[str, Type[OAuthProviderInterface]] = {
        "gmail": GmailOAuthProvider,
        "google-drive": GoogleDriveOAuthProvider,
        "google-calendar": GoogleCalendarOAuthProvider,
        "outlook": OutlookOAuthProvider,
        "onedrive": OneDriveOAuthProvider,
    }
    
    @classmethod
    def create_provider(cls, app_name: str) -> OAuthProviderInterface:
        """Create an OAuth provider instance for the given app name"""
        provider_class = cls._PROVIDER_MAP.get(app_name.lower())
        
        if not provider_class:
            logger.error(f"No OAuth provider found for app: {app_name}")
            raise ValueError(f"Unsupported OAuth app: {app_name}")
        
        oauth_settings = get_oauth_settings()
        provider = provider_class(oauth_settings)
        
        logger.info(f"Created OAuth provider for app: {app_name} -> {provider.provider_name}")
        return provider
    
    @classmethod
    def get_supported_apps(cls) -> list[str]:
        """Get list of supported OAuth app names"""
        return list(cls._PROVIDER_MAP.keys())
    
    @classmethod
    def is_supported_app(cls, app_name: str) -> bool:
        """Check if an app is supported for OAuth"""
        return app_name.lower() in cls._PROVIDER_MAP
