from abc import ABC, abstractmethod
from typing import Dict, Optional, List
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)


class OAuthTokenData(BaseModel):
    """Standard OAuth token data structure"""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    token_type: str = "Bearer"
    scope: Optional[str] = None


class OAuthInitiateResponse(BaseModel):
    """Response for OAuth initiation"""
    auth_url: str
    state: str


class OAuthCallbackResponse(BaseModel):
    """Response for OAuth callback handling"""
    tokens: OAuthTokenData
    user_info: Optional[Dict] = None


class OAuthProviderInterface(ABC):
    """Abstract interface for OAuth providers"""
    
    def __init__(self, oauth_settings):
        self.oauth_settings = oauth_settings
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the name of the OAuth provider"""
        pass
    
    @property
    @abstractmethod
    def default_scopes(self) -> List[str]:
        """Return the default scopes for this provider"""
        pass
    
    @abstractmethod
    async def build_auth_url(
        self, 
        scopes: List[str], 
        state: str, 
        additional_settings: Optional[Dict[str, str]] = None
    ) -> str:
        """Build the OAuth authorization URL"""
        pass
    
    @abstractmethod
    async def exchange_code_for_tokens(
        self, 
        code: str, 
        scopes: List[str]
    ) -> OAuthTokenData:
        """Exchange authorization code for access tokens"""
        pass
    
    @abstractmethod
    async def refresh_access_token(
        self, 
        refresh_token: str, 
        scopes: List[str]
    ) -> OAuthTokenData:
        """Refresh the access token using refresh token"""
        pass
    
    async def initiate_oauth_flow(
        self, 
        scopes: List[str], 
        state: str, 
        additional_settings: Optional[Dict[str, str]] = None
    ) -> OAuthInitiateResponse:
        """Initiate OAuth flow and return authorization URL"""
        self.logger.info(f"Initiating OAuth flow for {self.provider_name} with scopes: {scopes}")
        
        auth_url = await self.build_auth_url(scopes, state, additional_settings)
        
        return OAuthInitiateResponse(
            auth_url=auth_url,
            state=state
        )
    
    async def handle_oauth_callback(
        self, 
        code: str, 
        scopes: List[str]
    ) -> OAuthCallbackResponse:
        """Handle OAuth callback and return tokens"""
        self.logger.info(f"Handling OAuth callback for {self.provider_name}")
        
        tokens = await self.exchange_code_for_tokens(code, scopes)
        
        return OAuthCallbackResponse(
            tokens=tokens,
            user_info=None  # Can be extended by specific providers
        )
    
    async def refresh_token(
        self, 
        refresh_token: str, 
        scopes: List[str]
    ) -> OAuthTokenData:
        """Refresh access token"""
        self.logger.info(f"Refreshing token for {self.provider_name}")
        
        return await self.refresh_access_token(refresh_token, scopes)
    
    def get_credential_values(self, tokens: OAuthTokenData) -> Dict[str, str]:
        """Convert tokens to credential values format"""
        values = {
            "ACCESS_TOKEN": tokens.access_token,
            "TOKEN_TYPE": tokens.token_type
        }
        
        if tokens.refresh_token:
            values["REFRESH_TOKEN"] = tokens.refresh_token
        
        if tokens.expires_in:
            values["EXPIRES_IN"] = str(tokens.expires_in)
        
        if tokens.scope:
            values["SCOPE"] = tokens.scope
            
        return values
