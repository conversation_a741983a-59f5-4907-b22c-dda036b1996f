from typing import List
from app.services.oauth.providers.base_microsoft_oauth_provider import BaseMicrosoftOAuthProvider


class OneDriveOAuthProvider(BaseMicrosoftOAuthProvider):
    """OAuth provider for OneDrive"""
    
    @property
    def provider_name(self) -> str:
        return "onedrive"
    
    @property
    def default_scopes(self) -> List[str]:
        return [
            "openid",
            "profile", 
            "offline_access",
            "https://graph.microsoft.com/Files.ReadWrite",
            "https://graph.microsoft.com/Files.ReadWrite.All"
        ]
