from typing import Dict, Optional, List
import aiohttp
from fastapi import HTTPException
from app.services.oauth.oauth_provider_interface import (
    OAuthProviderInterface, 
    OAuthTokenData
)


class BaseMicrosoftOAuthProvider(OAuthProviderInterface):
    """Base OAuth provider for Microsoft services"""
    
    @property
    def provider_name(self) -> str:
        return "microsoft"
    
    @property
    def auth_url_base(self) -> str:
        return "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"
    
    @property
    def token_url(self) -> str:
        return "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    
    @property
    def client_id(self) -> str:
        return self.oauth_settings.microsoft_client_id
    
    @property
    def client_secret(self) -> str:
        return self.oauth_settings.microsoft_client_secret
    
    @property
    def redirect_uri(self) -> str:
        return self.oauth_settings.redirect_uri
    
    async def build_auth_url(
        self, 
        scopes: List[str], 
        state: str, 
        additional_settings: Optional[Dict[str, str]] = None
    ) -> str:
        """Build Microsoft OAuth authorization URL"""
        scope_string = " ".join(scopes)
        
        auth_url = (
            f"{self.auth_url_base}"
            f"?client_id={self.client_id}"
            f"&redirect_uri={self.redirect_uri}"
            f"&response_type=code"
            f"&scope={scope_string}"
            f"&state={state}"
            f"&response_mode=query"
        )
        
        # Add additional settings
        if additional_settings:
            for key, value in additional_settings.items():
                auth_url += f"&{key}={value}"
        
        return auth_url
    
    async def exchange_code_for_tokens(
        self, 
        code: str, 
        scopes: List[str]
    ) -> OAuthTokenData:
        """Exchange authorization code for Microsoft OAuth tokens"""
        scope_string = " ".join(scopes)
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.token_url,
                data={
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": self.redirect_uri,
                    "scope": scope_string
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    self.logger.error(f"Failed to exchange code for {self.provider_name}: {error_text}")
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Failed to exchange code: {error_text}"
                    )
                
                token_data = await response.json()
                
                return OAuthTokenData(
                    access_token=token_data["access_token"],
                    refresh_token=token_data.get("refresh_token"),
                    expires_in=token_data.get("expires_in"),
                    token_type=token_data.get("token_type", "Bearer"),
                    scope=token_data.get("scope")
                )
    
    async def refresh_access_token(
        self, 
        refresh_token: str, 
        scopes: List[str]
    ) -> OAuthTokenData:
        """Refresh Microsoft OAuth access token"""
        scope_string = " ".join(scopes)
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.token_url,
                data={
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "refresh_token": refresh_token,
                    "grant_type": "refresh_token",
                    "scope": scope_string
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    self.logger.error(f"Failed to refresh token for {self.provider_name}: {error_text}")
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Failed to refresh token: {error_text}"
                    )
                
                token_data = await response.json()
                
                return OAuthTokenData(
                    access_token=token_data["access_token"],
                    refresh_token=token_data.get("refresh_token", refresh_token),  # Keep original if not provided
                    expires_in=token_data.get("expires_in"),
                    token_type=token_data.get("token_type", "Bearer"),
                    scope=token_data.get("scope")
                )
