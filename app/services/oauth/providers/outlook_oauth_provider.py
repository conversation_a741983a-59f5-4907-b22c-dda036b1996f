from typing import List
from app.services.oauth.providers.base_microsoft_oauth_provider import BaseMicrosoftOAuthProvider


class OutlookOAuthProvider(BaseMicrosoftOAuthProvider):
    """OAuth provider for Outlook"""
    
    @property
    def provider_name(self) -> str:
        return "outlook"
    
    @property
    def default_scopes(self) -> List[str]:
        return [
            "openid",
            "profile", 
            "offline_access",
            "https://graph.microsoft.com/Mail.Read",
            "https://graph.microsoft.com/Mail.Send"
        ]
