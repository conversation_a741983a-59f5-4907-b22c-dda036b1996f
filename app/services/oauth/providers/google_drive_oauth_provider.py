from typing import List
from app.services.oauth.providers.base_google_oauth_provider import BaseGoogleOAuthProvider


class GoogleDriveOAuthProvider(BaseGoogleOAuthProvider):
    """OAuth provider for Google Drive"""
    
    @property
    def provider_name(self) -> str:
        return "google-drive"
    
    @property
    def default_scopes(self) -> List[str]:
        return [
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/drive.file"
        ]
