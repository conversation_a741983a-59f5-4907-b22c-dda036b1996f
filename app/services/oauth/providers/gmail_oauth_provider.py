from typing import List
from app.services.oauth.providers.base_google_oauth_provider import BaseGoogleOAuthProvider


class GmailOAuthProvider(BaseGoogleOAuthProvider):
    """OAuth provider for Gmail"""
    
    @property
    def provider_name(self) -> str:
        return "gmail"
    
    @property
    def default_scopes(self) -> List[str]:
        return [
            "https://www.googleapis.com/auth/gmail.send",
            "https://www.googleapis.com/auth/gmail.readonly"
        ]
