from typing import List
from app.services.oauth.providers.base_google_oauth_provider import BaseGoogleOAuthProvider


class GoogleCalendarOAuthProvider(BaseGoogleOAuthProvider):
    """OAuth provider for Google Calendar"""
    
    @property
    def provider_name(self) -> str:
        return "google-calendar"
    
    @property
    def default_scopes(self) -> List[str]:
        return [
            "https://www.googleapis.com/auth/calendar",
            "https://www.googleapis.com/auth/calendar.events"
        ]
