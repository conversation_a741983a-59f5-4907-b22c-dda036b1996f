import logging
from typing import Op<PERSON>, <PERSON>, Tuple, Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile
from app.repositories.blog_post_repository import BlogPostRepository
from app.models.blog_post_domain import BlogPost
from app.models.blog_post import B<PERSON><PERSON><PERSON><PERSON><PERSON>, BlogPostResponse, ImageUploadResponse
from app.services.storage_service import StorageService

logger = logging.getLogger(__name__)

class BlogPostService:
    """Service for blog post business logic"""
    
    def __init__(self, repository: BlogPostRepository):
        self.repository = repository
        self.storage_service = StorageService()
    
    def _to_response_dto(self, blog_post: BlogPost) -> BlogPostResponse:
        """Convert a blog post domain model to a response DTO"""
        try:
            data = blog_post.model_dump()
            
            # Ensure id is correctly handled
            if "_id" in data:
                data["id"] = data.pop("_id")
            
            # Create and return the DTO
            return BlogPostResponse(**data)
        except Exception as e:
            # Log the error and raise for proper handling
            logger.error(f"Error converting blog post to DTO: {str(e)}")
            logger.error(f"Blog post data: {blog_post.model_dump()}")
            raise
    
    async def upload_blog_post_image(
        self,
        blog_post_id: str,
        image: UploadFile
    ) -> ImageUploadResponse:
        """Upload an image for a blog post"""
        try:
            # Check if blog post exists
            blog_post = await self.repository.find_by_id(blog_post_id)
            if not blog_post:
                raise HTTPException(status_code=404, detail="Blog post not found")
            
            # Upload image to GCS
            image_url = await self.storage_service.upload_blog_post_image(
                blog_post_id,
                image
            )
            
            # Update blog post with new image URL
            blog_post.image = image_url
            await self.repository.update(blog_post_id, blog_post)
            
            return ImageUploadResponse(
                image_id=image_url.split("/")[-1],
                image_url=image_url,
                blog_post_id=blog_post_id
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading blog post image: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_blog_post_by_id(self, blog_post_id: str) -> Optional[BlogPostResponse]:
        """Get a blog post by ID"""
        try:
            blog_post = await self.repository.find_by_id(blog_post_id)
            if blog_post:
                return self._to_response_dto(blog_post)
            return None
        except Exception as e:
            logger.error(f"Error getting blog post by ID: {str(e)}")
            return None

    async def get_blog_post_by_slug(self, slug: str, locale: str = "en") -> Optional[BlogPostResponse]:
        """Get a blog post by slug and locale"""
        try:
            blog_post = await self.repository.find_by_slug(slug, locale)
            if blog_post:
                return self._to_response_dto(blog_post)
            return None
        except Exception as e:
            logger.error(f"Error getting blog post by slug: {str(e)}")
            return None

    async def get_blog_post_by_title(self, title: str) -> Optional[BlogPostResponse]:
        """Get a blog post by title"""
        try:
            blog_post = await self.repository.find_by_title(title)
            if blog_post:
                return self._to_response_dto(blog_post)
            return None
        except Exception as e:
            logger.error(f"Error getting blog post by title: {str(e)}")
            return None
    
    async def list_blog_posts(self, skip: int = 0, limit: int = 10) -> List[BlogPostResponse]:
        """List all blog posts with pagination"""
        try:
            blog_posts, _ = await self.repository.find_all(skip=skip, limit=limit)
            return [self._to_response_dto(blog_post) for blog_post in blog_posts]
        except Exception as e:
            logger.error(f"Error listing blog posts: {str(e)}")
            return []
    
    async def create_blog_post(self, dto: BlogPostCreate) -> BlogPostResponse:
        """Create a new blog post"""
        try:
            # Check if blog post with same title already exists
            existing_blog_post = await self.repository.find_by_title(dto.title)
            if existing_blog_post:
                raise HTTPException(
                    status_code=400,
                    detail="Blog post with this title already exists"
                )
            
            # Check if slug already exists for any locale
            for locale, slug in dto.slugs.items():
                if slug:  # Only check if slug is provided
                    existing_slug = await self.repository.find_by_slug(slug, locale)
                    if existing_slug:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Blog post with slug '{slug}' for locale '{locale}' already exists"
                        )
            
            # Create blog post
            blog_post = BlogPost(**dto.model_dump())
            blog_post_id = await self.repository.create(blog_post)
            
            if not blog_post_id:
                raise HTTPException(status_code=500, detail="Failed to create blog post")
            
            # Get the created blog post
            created_blog_post = await self.repository.find_by_id(blog_post_id)
            if not created_blog_post:
                raise HTTPException(status_code=500, detail="Failed to retrieve created blog post")
            
            return self._to_response_dto(created_blog_post)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating blog post: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_blog_post(self, blog_post_id: str, dto: BlogPostCreate) -> BlogPostResponse:
        """Update a blog post"""
        try:
            # Check if blog post exists
            existing_blog_post = await self.repository.find_by_id(blog_post_id)
            if not existing_blog_post:
                raise HTTPException(status_code=404, detail="Blog post not found")
            
            # Check if new title conflicts with another blog post
            if dto.title != existing_blog_post.title:
                title_conflict = await self.repository.find_by_title(dto.title)
                if title_conflict:
                    raise HTTPException(
                        status_code=400,
                        detail="Blog post with this title already exists"
                    )
            
            # Check if new slugs conflict with other blog posts
            for locale, new_slug in dto.slugs.items():
                if new_slug:  # Only check if slug is provided
                    existing_slug = await self.repository.find_by_slug(new_slug, locale)
                    if existing_slug and existing_slug.id != blog_post_id:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Blog post with slug '{new_slug}' for locale '{locale}' already exists"
                        )
            
            # Update blog post
            update_data = dto.model_dump()
            for key, value in update_data.items():
                setattr(existing_blog_post, key, value)
            
            # Update timestamp
            existing_blog_post.update_modified_at()
            
            # Save changes
            success = await self.repository.update(blog_post_id, existing_blog_post)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to update blog post")
            
            # Return updated blog post
            updated_blog_post = await self.repository.find_by_id(blog_post_id)
            if not updated_blog_post:
                raise HTTPException(status_code=500, detail="Failed to retrieve updated blog post")
            
            return self._to_response_dto(updated_blog_post)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating blog post: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def delete_blog_post(self, blog_post_id: str) -> bool:
        """Delete a blog post"""
        try:
            # Check if blog post exists
            blog_post = await self.repository.find_by_id(blog_post_id)
            if not blog_post:
                raise HTTPException(status_code=404, detail="Blog post not found")
            
            # Delete blog post
            return await self.repository.delete(blog_post_id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting blog post: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e)) 