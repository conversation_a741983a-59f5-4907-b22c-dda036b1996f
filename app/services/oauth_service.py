from fastapi import HTTP<PERSON>xception
from typing import Dict, Optional
import secrets
import logging
from datetime import datetime, timedelta, timezone
from app.database.mongodb import MongoDB
from app.services.app_service import AppService
from app.services.app_credential_service import AppCredentialService
from app.models.app_credential import AppCredentialCreate
from app.config.settings_loader import get_oauth_settings
from app.models.app import AuthMethod
from app.services.oauth.oauth_provider_factory import OAuthProviderFactory
# Set up logging
logger = logging.getLogger(__name__)

class OAuthService:
    def __init__(self, app_service: AppService, credential_service: AppCredentialService):
        self.app_service = app_service
        self.credential_service = credential_service
        self.db = MongoDB.get_db()
        self.oauth_settings = get_oauth_settings()
        self.provider_factory = OAuthProviderFactory

    async def initiate_oauth(self, user_id: str, workflow_id: str, app_id: str, variable_name: str, provider: str, redirect_to: str = None) -> Dict:
        """Initiate OAuth flow for a specific app variable"""
        logger.info(f"Initiating OAuth flow for user: {user_id}, app: {app_id}, variable: {variable_name}, provider: {provider}")

        # Get app details
        app = await self.app_service.get_app_by_id(app_id)
        if not app:
            logger.error(f"App {app_id} not found")
            raise HTTPException(status_code=404, detail=f"App {app_id} not found")

        if app.auth_method != AuthMethod.OAUTH:
            logger.error(f"App {app_id} is not OAuth-based")
            raise HTTPException(status_code=404, detail=f"App {app_id} is not OAuth-based")

        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)
        logger.info(f"Generated OAuth state: {state[:5]}...")

        # Create timestamps with timezone info
        created_at = datetime.now(timezone.utc)
        expires_at = created_at + timedelta(minutes=10)

        # Store OAuth state in database
        await self.db.oauth_states.insert_one({
            "state": state,
            "user_id": user_id,
            "app_id": app_id,
            "workflow_id": workflow_id,
            "variable_name": variable_name,
            "provider": provider,
            "redirect_to": redirect_to,
            "created_at": created_at,
            "expires_at": expires_at
        })

        logger.info(f"Stored OAuth state in database. Expires at: {expires_at.isoformat()}")

        # Use the new provider system
        try:
            oauth_provider = self.provider_factory.create_provider(provider)
        except ValueError as e:
            logger.error(f"Failed to create OAuth provider for {provider}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))

        # Get scopes and additional settings from app configuration
        scopes = app.oauth_provider.scopes if app.oauth_provider else oauth_provider.default_scopes
        additional_settings = app.oauth_provider.additional_settings if app.oauth_provider else {}

        # Initiate OAuth flow using the provider
        try:
            oauth_response = await oauth_provider.initiate_oauth_flow(
                scopes=scopes,
                state=state,
                additional_settings=additional_settings
            )

            return {
                "auth_url": oauth_response.auth_url,
                "state": oauth_response.state
            }
        except Exception as e:
            logger.error(f"Failed to initiate OAuth flow for {provider}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth flow: {str(e)}")

    async def handle_oauth_callback(self, code: str, state: str) -> Dict:
        """Handle OAuth callback and store credentials"""
        logger.info(f"Handling OAuth callback with state: {state[:5]}...")

        # Verify state
        oauth_state = await self.db.oauth_states.find_one({"state": state})
        if not oauth_state:
            logger.error(f"OAuth state not found in database: {state[:5]}...")
            # List recent states for debugging
            recent_states = await self.db.oauth_states.find().sort("created_at", -1).limit(5).to_list(length=5)
            if recent_states:
                logger.info(f"Recent states in database: {[s['state'][:5] + '...' for s in recent_states]}")
            else:
                logger.info("No recent OAuth states found in database")

            raise HTTPException(status_code=400, detail="Invalid or expired OAuth state")

        logger.info(f"Found OAuth state for app_id: {oauth_state.get('app_id')}, variable: {oauth_state.get('variable_name')}")

        # Ensure both datetimes are timezone-aware for comparison
        expires_at = oauth_state["expires_at"]
        # MongoDB stores datetimes in UTC, but we need to ensure it's timezone-aware
        if not expires_at.tzinfo:
            expires_at = expires_at.replace(tzinfo=timezone.utc)

        now = datetime.now(timezone.utc)
        logger.info(f"Current time: {now.isoformat()}, Expires at: {expires_at.isoformat()}")

        if now > expires_at:
            logger.warning(f"OAuth state expired. Created at: {oauth_state.get('created_at').isoformat()}")
            await self.db.oauth_states.delete_one({"state": state})
            raise HTTPException(status_code=400, detail="OAuth state expired")

        user_id = oauth_state["user_id"]
        workflow_id = oauth_state["workflow_id"]
        app_id = oauth_state["app_id"]
        variable_name = oauth_state["variable_name"]
        provider = oauth_state["provider"]

        # Use the new provider system for token exchange
        try:
            oauth_provider = self.provider_factory.create_provider(provider)
        except ValueError as e:
            logger.error(f"Failed to create OAuth provider for {provider}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))

        # Get app details to determine scopes
        app = await self.app_service.get_app_by_id(app_id)
        scopes = app.oauth_provider.scopes if app and app.oauth_provider else oauth_provider.default_scopes

        # Exchange code for tokens using the provider
        try:
            callback_response = await oauth_provider.handle_oauth_callback(code, scopes)
            tokens = callback_response.tokens

            # Convert tokens to credential values format
            values = oauth_provider.get_credential_values(tokens)

        except Exception as e:
            logger.error(f"Failed to exchange code for {provider}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Failed to exchange code: {str(e)}")

        # Common credential handling logic
        existing_credential = await self.credential_service.repository.find_by_user_and_app(user_id, app_id)

        if existing_credential:
            # Update existing credential
            if existing_credential.values:
                existing_credential.values.update(values)
            else:
                existing_credential.values = values
            existing_credential.update_modified_at()

            # Add workflow_id if not present or if workflow_ids attribute doesn't exist
            if hasattr(existing_credential, "workflow_ids") and existing_credential.workflow_ids is not None:
                if workflow_id and workflow_id not in existing_credential.workflow_ids:
                    existing_credential.workflow_ids.append(workflow_id)
            else: # Handles None or missing attribute
                if workflow_id:
                    existing_credential.workflow_ids = [workflow_id]

            await self.credential_service.repository.update(
                str(existing_credential.id),
                existing_credential
            )
            logger.info(f"Updated {provider.capitalize()} OAuth credential for user {user_id}, app {app_id}")
        else:
            # Create new credential
            credential_payload = {
                "user_id": user_id,
                "app_id": app_id,
                "values": values,
                "workflow_ids": [workflow_id] if workflow_id else []
            }
            credential = AppCredentialCreate(**credential_payload)
            await self.credential_service.create_credential(credential)
            logger.info(f"Created new {provider.capitalize()} OAuth credential for user {user_id}, app {app_id}")

        # Clean up state
        await self.db.oauth_states.delete_one({"state": state})
        logger.info(f"OAuth state {state[:5]}... deleted for {provider.capitalize()} callback.")

        return {"success": True, "provider": provider, "variable": variable_name, "app_id": app_id, "workflow_id": workflow_id}

    async def check_oauth_status(self, user_id: str, app_id: str) -> Dict[str, str]:
        """Check OAuth status for all variables of an app"""
        # Get app details
        app = await self.app_service.get_app_by_id(app_id)
        if not app:
            raise HTTPException(status_code=404, detail=f"App {app_id} not found")

        if app.auth_method != AuthMethod.OAUTH:
            logger.error(f"App {app_id} is not OAuth-based")
            raise HTTPException(status_code=404, detail=f"App {app_id} is not OAuth-based")

        # Get user credentials for this app
        credential = await self.credential_service.repository.find_by_user_and_app(user_id, app_id)
        return {
            "connection_status": "connected" if credential is not None else "not_connected"
        }

    async def refresh_access_token(self, user_id: str, app_id: str, provider: str) -> Dict:
        """Refresh OAuth access token for a specific app and provider"""
        logger.info(f"Refreshing access token for user: {user_id}, app: {app_id}, provider: {provider}")

        credential = await self.credential_service.repository.find_by_user_and_app(user_id, app_id)
        if not credential or not credential.values:
            logger.error(f"No credentials found for user {user_id}, app {app_id} to refresh.")
            raise HTTPException(status_code=404, detail="Credentials not found or empty.")

        # Use the new provider system
        try:
            oauth_provider = self.provider_factory.create_provider(provider)
        except ValueError as e:
            logger.error(f"Failed to create OAuth provider for {provider}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))

        # Get refresh token from credentials
        refresh_token = credential.values.get("REFRESH_TOKEN")
        if not refresh_token:
            logger.error(f"REFRESH_TOKEN not found for user {user_id}, app {app_id}.")
            raise HTTPException(status_code=400, detail=f"Refresh token not found for {provider}.")

        # Get app details to determine scopes
        app_details = await self.app_service.get_app_by_id(app_id)
        scopes = app_details.oauth_provider.scopes if app_details and app_details.oauth_provider else oauth_provider.default_scopes

        # Check if token needs refreshing based on expiration time
        expires_in_str = credential.values.get("EXPIRES_IN")
        if expires_in_str and hasattr(credential, "updated_at"):
            try:
                # Convert expires_in from string to int (seconds)
                expires_in_seconds = int(expires_in_str)

                # Calculate when the token will expire
                if credential.updated_at.tzinfo is None:
                    credential_updated_at = credential.updated_at.replace(tzinfo=timezone.utc)
                else:
                    credential_updated_at = credential.updated_at

                expiration_time = credential_updated_at + timedelta(seconds=expires_in_seconds)
                current_time = datetime.now(timezone.utc)

                # Add 20 minutes margin
                margin = timedelta(minutes=20)
                refresh_threshold = expiration_time - margin

                # If token is not close to expiration, return without refreshing
                if current_time < refresh_threshold:
                    logger.info(f"Token for user {user_id}, app {app_id}, provider {provider} doesn't need refreshing yet. Expires at: {expiration_time.isoformat()}, current time: {current_time.isoformat()}")
                    return {
                        "status": "not_needed",
                        "provider": provider,
                        "app_id": app_id,
                        "message": f"{provider.capitalize()} token is still valid."
                    }

                logger.info(f"Token for user {user_id}, app {app_id}, provider {provider} needs refreshing. Expires at: {expiration_time.isoformat()}, current time: {current_time.isoformat()}")
            except (ValueError, TypeError) as e:
                # If there's an error parsing the expires_in value, log it but continue with refresh
                logger.warning(f"Error calculating token expiration for {provider}: {str(e)}. Proceeding with refresh anyway.")

        # Use the provider to refresh the token
        try:
            refreshed_tokens = await oauth_provider.refresh_token(refresh_token, scopes)

            # Convert tokens to credential values format
            new_values = oauth_provider.get_credential_values(refreshed_tokens)

        except Exception as e:
            logger.error(f"Failed to refresh {provider} token for user {user_id}, app {app_id}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Failed to refresh {provider} token: {str(e)}")

        if not new_values:
            logger.warning(f"Token refresh for {provider} did not return new token information for user {user_id}, app {app_id}.")
            return {"status": "no_update", "message": "Token refresh did not yield new token information."}

        # Update existing credential values
        credential.values.update(new_values)
        credential.update_modified_at()

        await self.credential_service.repository.update(
            str(credential.id),
            credential
        )
        logger.info(f"Successfully updated credentials after {provider} token refresh for user {user_id}, app {app_id}.")

        return {"status": "success", "provider": provider, "app_id": app_id, "message": f"{provider.capitalize()} token refreshed successfully."}
