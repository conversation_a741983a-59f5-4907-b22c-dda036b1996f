import logging
from typing import Op<PERSON>, List, Tuple, Dict, Any
from fastapi import HTTPException

from app.repositories.app_repository import AppRepository
from app.models.app_domain import App, AuthMethod
from app.models.app import AppCreate, AppResponse, AppCredential, VariableLocation, RuntimeEnvironment
from app.models.agent_runner import McpServerConfig

logger = logging.getLogger(__name__)

class AppService:
    """Service for app business logic"""

    def __init__(self, repository: AppRepository):
        self.repository = repository

    def _to_response_dto(self, app: App) -> AppResponse:
        """Convert an app domain model to a response DTO"""
        try:
            data = app.model_dump()

            # Ensure id is correctly handled
            if "_id" in data:
                data["id"] = data.pop("_id")

            # Create and return the DTO
            return AppResponse(**data)
        except Exception as e:
            # Log the error and raise for proper handling
            logger.error(f"Error converting app to DTO: {str(e)}")
            logger.error(f"App data: {app.model_dump()}")
            raise

    async def get_app_by_id(self, app_id: str) -> Optional[AppResponse]:
        """Get an app by ID"""
        try:
            app = await self.repository.find_by_id(app_id)
            if app:
                return self._to_response_dto(app)
            return None
        except Exception as e:
            logger.error(f"Error getting app by ID: {str(e)}")
            return None

    async def get_app_by_name(self, name: str) -> Optional[AppResponse]:
        """Get an app by name"""
        try:
            app = await self.repository.find_by_name(name)
            if app:
                return self._to_response_dto(app)
            return None
        except Exception as e:
            logger.error(f"Error getting app by name: {str(e)}")
            return None

    async def list_apps(self) -> List[AppResponse]:
        """List all apps"""
        try:
            apps, _ = await self.repository.find_all()
            return [self._to_response_dto(app) for app in apps]
        except Exception as e:
            logger.error(f"Error listing apps: {str(e)}")
            return []

    async def create_app(self, dto: AppCreate) -> AppResponse:
        """Create a new app"""
        try:
            dto.name = dto.name.lower()
            # Check if app with same name already exists
            existing_app = await self.repository.find_by_name(dto.name)
            if existing_app:
                raise HTTPException(
                    status_code=400,
                    detail="App with this name already exists"
                )

            # Create app
            app = App(**dto.model_dump())
            app_id = await self.repository.create(app)

            if not app_id:
                raise HTTPException(status_code=500, detail="Failed to create app")

            # Get the created app
            created_app = await self.repository.find_by_id(app_id)
            if not created_app:
                raise HTTPException(status_code=500, detail="Failed to retrieve created app")

            return self._to_response_dto(created_app)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating app: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def update_app(self, app_id: str, dto: AppCreate) -> AppResponse:
        """Update an app"""
        try:
            # Check if app exists
            existing_app = await self.repository.find_by_id(app_id)
            if not existing_app:
                raise HTTPException(status_code=404, detail="App not found")

            # Check if new name conflicts with another app
            if dto.name != existing_app.name:
                name_conflict = await self.repository.find_by_name(dto.name)
                if name_conflict:
                    raise HTTPException(
                        status_code=400,
                        detail="App with this name already exists"
                    )

            # Update app
            update_data = dto.model_dump()
            for key, value in update_data.items():
                setattr(existing_app, key, value)

            # Update timestamp
            existing_app.update_modified_at()

            # Save changes
            success = await self.repository.update(app_id, existing_app)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to update app")

            # Return updated app
            updated_app = await self.repository.find_by_id(app_id)
            if not updated_app:
                raise HTTPException(status_code=500, detail="Failed to retrieve updated app")

            return self._to_response_dto(updated_app)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating app: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_app(self, app_id: str) -> bool:
        """Delete an app"""
        try:
            # Check if app exists
            app = await self.repository.find_by_id(app_id)
            if not app:
                raise HTTPException(status_code=404, detail="App not found")

            # Delete app
            return await self.repository.delete(app_id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting app: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def validate_app_credentials(self, app_id: str, credentials: AppCredential) -> Dict[str, str]:
        """
        Validates app credentials against the app's auth_variables definition.
        Returns a dictionary of validated credentials.
        Raises HTTPException if validation fails.
        """
        app = await self.get_app_by_id(app_id)
        if not app:
            raise HTTPException(status_code=404, detail=f"App {app_id} not found")

        # Create a map of variable definitions by name for easy lookup
        var_defs = {var.name: var for var in app.auth_variables}

        # Validate all required variables are provided
        missing_vars = []
        for var_def in app.auth_variables:
            if var_def.required and not any(v.name == var_def.name for v in credentials.variables):
                missing_vars.append(var_def.name)

        if missing_vars:
            raise HTTPException(
                status_code=400,
                detail=f"Missing required variables for app {app.name}: {', '.join(missing_vars)}"
            )

        # Validate each provided variable
        validated_values = {}
        for var in credentials.variables:
            var_def = var_defs.get(var.name)
            # Store the validated value with its definition, including arg_format
            if var_def:
                validated_values[var.name] = {
                    "value": var.value,
                    "type": var_def.type,
                    "location": var_def.location,
                    "arg_prefix": var_def.arg_prefix,
                    "arg_format": getattr(var_def, "arg_format", "space")
                }

        return validated_values

    async def get_app_configurations_with_credentials(self, app_ids: List[str], user_id: str, workflow_id: Optional[str] = None) -> List[McpServerConfig]:
        """
        Process a list of app IDs and return their final configurations with credentials.

        Args:
            app_ids: List of app IDs to process
            user_id: ID of the user whose credentials to use
            workflow_id: Optional ID of the workflow to use for finding specific credentials

        Returns:
            List of app configurations with credentials
        """
        from app.core.dependencies import get_app_credential_service, get_app_credential_repository, get_oauth_service
        from app.database.mongodb import MongoDB

        # Get app credential service
        db = MongoDB.get_db()
        app_credential_service = await get_app_credential_service(
            await get_app_credential_repository(db),
            self
        )
        oauth_service = await get_oauth_service(
            self,
            app_credential_service
        )

        processed_apps = []

        for app_id in app_ids:
            # Get the app for its configuration
            app = await self.get_app_by_id(app_id)
            if not app:
                logger.warning(f"App {app_id} not found, skipping")
                continue
            
            if app.auth_method == AuthMethod.OAUTH:
                print(f"App {app_id} is using OAuth")
                await oauth_service.refresh_access_token(user_id=user_id, app_id=app_id, provider=app.oauth_provider.type)

            # Get the best credential for this context
            credential = await app_credential_service.get_best_credential_for_context(
                app_id=app_id,
                user_id=user_id,
                workflow_id=workflow_id
            )

            if not credential:
                logger.warning(f"No credentials found for user {user_id} and app {app_id}, skipping")
                continue

            # Convert credential values to AppCredential format for validation
            app_cred = AppCredential(
                app_id=app_id,
                variables=[{"name": k, "value": v} for k, v in credential.values.items()]
            )

            # Validate credentials against app's auth_variables
            try:
                validated_creds = await self.validate_app_credentials(app_id, app_cred)
            except HTTPException as e:
                logger.warning(f"Invalid credentials for app {app_id}: {e.detail}, skipping")
                continue

            # Start with base configuration
            args = list(app.args)  # Create a copy of base args
            env = {}
            # Process each validated variable
            for var_name, var_info in validated_creds.items():
                if var_info["location"] == VariableLocation.ENV:
                    env[var_name] = var_info["value"]
                else:  # arg
                    arg_prefix = var_info["arg_prefix"]
                    arg_format = var_info.get("arg_format", "space")
                    if arg_format == "equals" and arg_prefix:
                        args.append(f"{arg_prefix}={var_info['value']}")
                    elif arg_prefix:
                        args.extend([arg_prefix, var_info["value"]])
                    else:
                        args.append(var_info["value"])

            # Create the final app configuration
            app_config = McpServerConfig(
                id=app_id,
                name=app.name,
                transport=app.transport,
                runtime=app.command,
                args=args,
                env=env
            )

            # Add Docker-specific fields if transport is docker
            if app.runtime_env == RuntimeEnvironment.DOCKER:
                if app.git_url is None or app.docker_file_path is None:
                    logger.warning(f"Docker app {app.name} is missing required git_url or docker_file_path, skipping")
                    continue
                app_config.git_url = app.git_url
                app_config.docker_file_path = app.docker_file_path

            processed_apps.append(app_config)

        return processed_apps