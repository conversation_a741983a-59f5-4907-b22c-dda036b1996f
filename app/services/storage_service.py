import logging
import uuid
from google.cloud import storage
from fastapi import HTTPException, UploadFile
from typing import Optional

logger = logging.getLogger(__name__)

class StorageService:
    """Service for handling storage operations"""
    
    def __init__(self, bucket_name: str = "morrow-blog-posts-public"):
        self.bucket_name = bucket_name
        self.client = storage.Client()
        self.bucket = self.client.bucket(bucket_name)
    
    async def upload_blog_post_image(
        self,
        blog_post_id: str,
        image: UploadFile,
        content_type: str = "image/jpeg"
    ) -> str:
        """Upload an image for a blog post to GCS"""
        try:
            # Generate a unique image ID
            image_id = str(uuid.uuid4())
            
            # Create the blob path
            blob_path = f"posts/{blog_post_id}/images/{image_id}"
            
            # Create the blob
            blob = self.bucket.blob(blob_path)
            
            # Upload the file
            content = await image.read()
            blob.upload_from_string(
                content,
                content_type=content_type
            )
            
            # Make the blob publicly accessible
            blob.make_public()
            
            # Return the public URL
            return blob.public_url
            
        except Exception as e:
            logger.error(f"Error uploading image to GCS: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to upload image"
            )
    
    async def delete_blog_post_image(self, blog_post_id: str, image_id: str) -> bool:
        """Delete an image from GCS"""
        try:
            # Create the blob path
            blob_path = f"posts/{blog_post_id}/images/{image_id}"
            
            # Get the blob
            blob = self.bucket.blob(blob_path)
            
            # Delete the blob
            blob.delete()
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting image from GCS: {str(e)}")
            return False 