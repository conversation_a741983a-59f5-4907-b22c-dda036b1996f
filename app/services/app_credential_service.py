import logging
from typing import Op<PERSON>, <PERSON>, Tuple, Dict
from fastapi import HTTPEx<PERSON>

from app.repositories.app_credential_repository import AppCredentialRepository
from app.services.app_service import AppService
from app.models.app_credential_domain import AppCredential
from app.models.app_credential import AppCredentialCreate, AppCredentialUpdate, AppCredentialResponse

logger = logging.getLogger(__name__)

class AppCredentialService:
    """Service for app credential management"""

    def __init__(self, repository: AppCredentialRepository, app_service: AppService):
        self.repository = repository
        self.app_service = app_service

    # DTO Conversion Methods
    # ---------------------

    def _to_response_dto(self, credential: AppCredential) -> AppCredentialResponse:
        """Convert an app credential domain model to a response DTO"""
        try:
            data = credential.model_dump()

            # Ensure id is correctly handled
            if "_id" in data:
                data["id"] = data.pop("_id")

            # Create and return the DTO
            return AppCredentialResponse(**data)
        except Exception as e:
            logger.error(f"Error converting app credential to DTO: {str(e)}")
            logger.error(f"App credential data: {credential.model_dump()}")
            raise HTTPException(status_code=500, detail=f"Failed to convert credential to DTO: {str(e)}")

    # Core Credential Management Methods
    # --------------------------------

    async def get_credential_by_id(self, credential_id: str) -> AppCredentialResponse:
        """
        Get an app credential by ID

        Args:
            credential_id: ID of the credential to retrieve

        Returns:
            The credential as a response DTO

        Raises:
            HTTPException: If the credential is not found or an error occurs
        """
        try:
            credential = await self.repository.find_by_id(credential_id)
            if not credential:
                raise HTTPException(status_code=404, detail=f"Credential with ID {credential_id} not found")
            return self._to_response_dto(credential)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting app credential by ID: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get credential: {str(e)}")

    async def create_credential(self, dto: AppCredentialCreate) -> AppCredentialResponse:
        """
        Create a new app credential

        Args:
            dto: Data for creating the credential

        Returns:
            The created credential as a response DTO

        Raises:
            HTTPException: If a credential for this user and app already exists or creation fails
        """
        try:
            # Check if credential for this user and app already exists
            existing_credential = await self.repository.find_by_user_and_app(dto.user_id, dto.app_id)
            if existing_credential:
                raise HTTPException(
                    status_code=400,
                    detail=f"Credential for user {dto.user_id} and app {dto.app_id} already exists"
                )

            # Create credential
            credential = AppCredential(**dto.model_dump())
            credential_id = await self.repository.create(credential)

            if not credential_id:
                raise HTTPException(status_code=500, detail="Failed to create app credential")

            # Get the created credential
            created_credential = await self.repository.find_by_id(credential_id)
            if not created_credential:
                raise HTTPException(status_code=500, detail="Failed to retrieve created app credential")

            return self._to_response_dto(created_credential)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating app credential: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create credential: {str(e)}")

    async def update_credential(self, credential_id: str, dto: AppCredentialUpdate) -> AppCredentialResponse:
        """
        Update an app credential

        Args:
            credential_id: ID of the credential to update
            dto: Data for updating the credential

        Returns:
            The updated credential as a response DTO

        Raises:
            HTTPException: If the credential is not found or update fails
        """
        try:
            # Check if credential exists
            existing_credential = await self.repository.find_by_id(credential_id)
            if not existing_credential:
                raise HTTPException(status_code=404, detail=f"Credential with ID {credential_id} not found")

            # Update credential
            update_data = dto.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(existing_credential, key, value)

            existing_credential.update_modified_at()
            success = await self.repository.update(credential_id, existing_credential)

            if not success:
                raise HTTPException(status_code=500, detail="Failed to update app credential")

            # Get the updated credential
            updated_credential = await self.repository.find_by_id(credential_id)
            if not updated_credential:
                raise HTTPException(status_code=500, detail="Failed to retrieve updated app credential")

            return self._to_response_dto(updated_credential)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating app credential: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update credential: {str(e)}")

    # Workflow Association Methods
    # --------------------------
    async def associate_credential_with_workflow(self, credential_id: str, workflow_id: str) -> bool:
        """
        Associate a credential with a workflow

        Args:
            credential_id: ID of the credential to associate
            workflow_id: ID of the workflow to associate with

        Returns:
            True if the association was successful, False otherwise

        Raises:
            HTTPException: If the credential is not found or association fails
        """
        try:
            success = await self.repository.add_workflow_id(credential_id, workflow_id)
            if not success:
                raise HTTPException(status_code=404, detail=f"Credential with ID {credential_id} not found")
            return success
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error associating workflow with credential: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to associate workflow with credential: {str(e)}")

    # Context-Aware Credential Retrieval
    # --------------------------------

    async def get_best_credential_for_context(
        self,
        app_id: str,
        user_id: str,
        workflow_id: Optional[str] = None
    ) -> Optional[AppCredentialResponse]:
        """
        Get the best credential for the given context.

        This method encapsulates the complex logic of finding the right credential
        based on the context (user, app, and optionally workflow).

        Args:
            app_id: ID of the app to get credentials for
            user_id: ID of the user to get credentials for
            workflow_id: Optional ID of the workflow to get credentials for

        Returns:
            The best credential for the context, or None if no credential is found
        """
        try:
            # First try workflow-specific credential
            if workflow_id:
                workflow_credential = await self._find_workflow_specific_credential(
                    workflow_id, app_id, user_id
                )
                if workflow_credential:
                    return self._to_response_dto(workflow_credential)

            # Fall back to general credential
            general_credential = await self.repository.find_by_user_and_app(user_id, app_id)
            if general_credential:
                return self._to_response_dto(general_credential)

            return None
        except Exception as e:
            logger.error(f"Error getting best credential for context: {str(e)}")
            return None

    async def _find_workflow_specific_credential(
        self,
        workflow_id: str,
        app_id: str,
        user_id: str
    ) -> Optional[AppCredential]:
        """
        Find a credential specific to a workflow.

        This is a private helper method that encapsulates the logic of finding
        a workflow-specific credential.

        Args:
            workflow_id: ID of the workflow to find credentials for
            app_id: ID of the app to find credentials for
            user_id: ID of the user to find credentials for

        Returns:
            The workflow-specific credential, or None if no credential is found
        """
        try:
            # Get credentials associated with this workflow
            workflow_credentials, _ = await self.repository.find_by_workflow_id(workflow_id)

            # Find the credential for this app and user
            for credential in workflow_credentials:
                if credential.app_id == app_id and credential.user_id == user_id:
                    return credential

            return None
        except Exception as e:
            logger.error(f"Error finding workflow-specific credential: {str(e)}")
            return None


    # Credential Processing
    # -------------------

    async def save_app_credential(
        self,
        app_id: str,
        user_id: str,
        values: Dict[str, str],
        workflow_id: Optional[str] = None
    ) -> Tuple[Optional[AppCredentialResponse], List[str]]:
        """
        Save or update app credentials.

        This method handles creating new credentials or updating existing ones,
        and associates them with a workflow if specified.

        Args:
            app_id: ID of the app to save credentials for
            user_id: ID of the user to save credentials for
            values: Key-value pairs of credential values
            workflow_id: Optional ID of the workflow to associate with the credential

        Returns:
            A tuple of (credential_response, validation_errors)
        """
        validation_errors = []

        try:
            # Check if app exists
            app = await self.app_service.get_app_by_id(app_id)
            if not app:
                validation_errors.append(f"App with ID {app_id} not found")
                return None, validation_errors

            # Check if credential already exists
            existing_credential = await self.repository.find_by_user_and_app(user_id, app_id)

            # Create or update credential
            if existing_credential:
                # Update existing credential
                update_dto = AppCredentialUpdate(values=values)
                if workflow_id:
                    # Add workflow ID if not already present
                    if workflow_id not in existing_credential.workflow_ids:
                        await self.associate_credential_with_workflow(
                            str(existing_credential.id), workflow_id
                        )

                # Update the credential
                credential_response = await self.update_credential(
                    str(existing_credential.id), update_dto
                )
            else:
                # Create new credential
                create_dto = AppCredentialCreate(
                    user_id=user_id,
                    app_id=app_id,
                    values=values,
                    workflow_ids=[workflow_id] if workflow_id else []
                )
                credential_response = await self.create_credential(create_dto)

            return credential_response, validation_errors
        except HTTPException as e:
            validation_errors.append(e.detail)
            return None, validation_errors
        except Exception as e:
            logger.error(f"Error saving app credential: {str(e)}")
            validation_errors.append(f"Unexpected error: {str(e)}")
            return None, validation_errors
