import json
from typing import Optional
import asyncio
from functools import partial
from app.config.settings_loader import get_core_settings

if get_core_settings().use_pubsub:
    from google.cloud import pubsub_v1

class LoggingService:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggingService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        self.use_pubsub = get_core_settings().use_pubsub

        if self._initialized:
            return
        
        if self.use_pubsub:
            self.publisher = pubsub_v1.PublisherClient()
            self.topic_path = "projects/heroic-tide-455418-v9/topics/app-logs-topic"
            self.service_name = "backend-service"

        self._initialized = True

    async def test_publishing(self) -> bool:
        """
        Test if publishing to Pub/Sub works correctly.
        Returns True if successful, False otherwise.
        """
        test_data = {
            "workflow_run_id": "test-123",
            "service_name": self.service_name,
            "severity": "INFO",
            "message": "Test message"
        }
        
        try:
            data = json.dumps(test_data).encode("utf-8")
            future = self.publisher.publish(self.topic_path, data)
            # Wait for the publish to complete
            future.result(timeout=5)
            return True
        except Exception as e:
            print(f"Test publishing failed: {str(e)}")
            return False

    async def log_message(
        self,
        workflow_run_id: str,
        message: str,
        severity: str = "INFO"
    ) -> None:
        """
        Asynchronously publish a log message to GCP Pub/Sub.
        This method is non-blocking and won't interfere with the application flow.
        
        Args:
            workflow_run_id: The ID of the workflow run
            message: The log message
            severity: The severity level (INFO, WARNING, ERROR, etc.)
        """
        log_data = {
            "workflow_run_id": workflow_run_id,
            "service_name": self.service_name,
            "severity": severity,
            "message": message
        }

        # Convert to JSON string
        data = json.dumps(log_data).encode("utf-8")

        # Run the publish operation in a thread pool to avoid blocking
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                partial(self.publisher.publish, self.topic_path, data)
            )
        except Exception as e:
            # Catch any exceptions to ensure the main flow isn't affected
            print(f"Failed to publish log message: {str(e)}")

# Create a global instance
logging_service = LoggingService() 