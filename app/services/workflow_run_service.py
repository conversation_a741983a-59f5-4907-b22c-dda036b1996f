import logging
from typing import Optional, List, Tuple, Dict, Any
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from datetime import datetime, UTC

from app.repositories.workflow_run_repository import WorkflowRunRepository
from app.models.workflow_run import WorkflowRun, WorkflowStatus

logger = logging.getLogger(__name__)

class WorkflowRunService:
    """Service for workflow run operations"""
    
    def __init__(self, repository: WorkflowRunRepository):
        self.repository = repository

    async def get_workflow_run(self, run_id: str) -> Optional[WorkflowRun]:
        """Get a workflow run by ID"""
        try:
            return await self.repository.find_by_id(run_id)
        except Exception as e:
            logger.error(f"Error getting workflow run by ID: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def list_workflow_runs(
        self, 
        workflow_id: Optional[str] = None,
        status: Optional[WorkflowStatus] = None,
        skip: int = 0, 
        limit: int = 10
    ) -> <PERSON><PERSON>[List[WorkflowRun], int]:
        """List workflow runs with optional filtering"""
        try:
            if workflow_id:
                return await self.repository.find_by_workflow_id(workflow_id, skip, limit)
            elif status:
                return await self.repository.find_by_status(status, skip, limit)
            else:
                return await self.repository.find_all(skip, limit)
        except Exception as e:
            logger.error(f"Error listing workflow runs: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def create_workflow_run(self, workflow_id: str) -> WorkflowRun:
        """Create a new workflow run"""
        try:
            workflow_run = WorkflowRun(
                workflow_id=workflow_id,
                status=WorkflowStatus.PENDING,
                scheduled_time=datetime.now(UTC)
            )
            run_id = await self.repository.create(workflow_run)
            if not run_id:
                raise HTTPException(status_code=500, detail="Failed to create workflow run")
            workflow_run.id = run_id
            return workflow_run
        except Exception as e:
            logger.error(f"Error creating workflow run: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def update_workflow_run_status(
        self, 
        run_id: str, 
        status: WorkflowStatus,
        error_message: Optional[str] = None
    ) -> bool:
        """Update workflow run status"""
        try:
            success = await self.repository.update_status(run_id, status, error_message)
            if not success:
                raise HTTPException(status_code=404, detail="Workflow run not found")
            return success
        except Exception as e:
            logger.error(f"Error updating workflow run status: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_workflow_run(self, run_id: str) -> bool:
        """Delete a workflow run"""
        try:
            success = await self.repository.delete(run_id)
            if not success:
                raise HTTPException(status_code=404, detail="Workflow run not found")
            return success
        except Exception as e:
            logger.error(f"Error deleting workflow run: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def get_workflow_logs(
        self,
        run_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """Get logs for a specific workflow run"""
        try:
            # Verify workflow run exists
            workflow_run = await self.get_workflow_run(run_id)
            if not workflow_run:
                raise HTTPException(status_code=404, detail="Workflow run not found")
            
            return await self.repository.get_workflow_logs(run_id, skip, limit)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting workflow logs: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e)) 