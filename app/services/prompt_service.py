from app.repositories.prompt_repository import PromptRepository
from app.models.prompt_domain import Prompt
from app.models.prompt import PromptCreate, PromptUpdate, PromptResponse
from fastapi import HTTPException

class PromptService:
    def __init__(self, repository: PromptRepository):
        self.repository = repository

    def _to_response(self, prompt: Prompt) -> PromptResponse:
        data = prompt.model_dump()
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        return PromptResponse(**data)

    async def create_prompt(self, dto: PromptCreate) -> PromptResponse:
        prompt = Prompt(**dto.model_dump())
        prompt_id = await self.repository.create(prompt)
        if not prompt_id:
            raise HTTPException(500, "Failed to create prompt")
        created = await self.repository.find_by_id(prompt_id)
        return self._to_response(created)

    async def get_prompt(self, prompt_id: str) -> PromptResponse:
        prompt = await self.repository.find_by_id(prompt_id)
        if not prompt:
            raise HTTPException(404, "Prompt not found")
        return self._to_response(prompt)

    async def list_prompts(self, skip=0, limit=10):
        prompts, _ = await self.repository.find_all(skip=skip, limit=limit)
        return [self._to_response(p) for p in prompts]

    async def update_prompt(self, prompt_id: str, dto: PromptUpdate) -> PromptResponse:
        prompt = await self.repository.find_by_id(prompt_id)
        if not prompt:
            raise HTTPException(404, "Prompt not found")
        for k, v in dto.model_dump(exclude_unset=True).items():
            setattr(prompt, k, v)
        prompt.update_modified_at()
        await self.repository.update(prompt_id, prompt)
        updated = await self.repository.find_by_id(prompt_id)
        return self._to_response(updated)

    async def delete_prompt(self, prompt_id: str) -> bool:
        return await self.repository.delete(prompt_id)

    async def get_prompt_by_name(self, name: str) -> PromptResponse:
        prompt = await self.repository.find_by_name(name)
        if not prompt:
            raise HTTPException(404, "Prompt not found")
        return self._to_response(prompt)

    async def get_prompt_content_by_name(self, name: str) -> str:
        prompt = await self.repository.find_by_name(name)
        if not prompt:
            raise HTTPException(404, "Prompt not found")
        return prompt.content 