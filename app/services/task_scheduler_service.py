import json
from datetime import datetime, UTC
from typing import Optional
from uuid import UUID
from google.cloud import tasks_v2
from google.protobuf import timestamp_pb2

from app.models.workflow_run import WorkflowRun, WorkflowStatus
from app.config.gcp_settings import get_gcp_settings
from app.repositories.workflow_run_repository import WorkflowRunRepository

class TaskSchedulerService:
    def __init__(self, workflow_run_repository: WorkflowRunRepository):
        self.gcp_settings = get_gcp_settings()
        self.client = tasks_v2.CloudTasksClient()
        self.queue_path = self.client.queue_path(
            self.gcp_settings.project_id,
            self.gcp_settings.location,
            self.gcp_settings.tasks_queue
        )
        self.workflow_run_repository = workflow_run_repository

    async def schedule_workflow(self, workflow_id: str) -> WorkflowRun:
        """
        Schedule a workflow to be executed by creating a Cloud Task
        
        Args:
            workflow_id: The ID of the workflow to execute
            
        Returns:
            WorkflowRun: The created workflow run instance
        """
        # Create a new workflow run record
        workflow_run = WorkflowRun(
            workflow_id=workflow_id,
            status=WorkflowStatus.SCHEDULED,
            scheduled_time=datetime.now(UTC)
        )

        try:
            # Save the workflow run to the database first to get the ID
            run_id = await self.workflow_run_repository.create(workflow_run)
            if not run_id:
                raise Exception("Failed to save workflow run")
            
            # Update the workflow run with the database ID
            workflow_run.id = run_id

            # Create the task payload and encode it properly
            task_payload = {
                "workflow_id": str(workflow_id),
                "workflow_run_id": str(workflow_run.id)
            }
            
            # Create timestamp for immediate execution
            timestamp = timestamp_pb2.Timestamp()
            timestamp.FromDatetime(datetime.now(UTC))
            
            # Create the Cloud Task
            task = {
                "http_request": {
                    "http_method": tasks_v2.HttpMethod.POST,
                    "url": f"{self.gcp_settings.agent_runner_service_endpoint}/v1/agent/execute",
                    "headers": {
                        "Content-Type": "application/json",
                    },
                    "body": json.dumps(task_payload).encode('utf-8'),
                },
                "schedule_time": timestamp  # Set schedule time to now for immediate execution
            }

            # Create the task in Cloud Tasks
            response = self.client.create_task(
                request={
                    "parent": self.queue_path,
                    "task": task
                }
            )
            
            return workflow_run
            
        except Exception as e:
            workflow_run.status = WorkflowStatus.FAILED
            workflow_run.error_message = str(e)
            # Try to save the failed state
            try:
                if not workflow_run.id:
                    await self.workflow_run_repository.create(workflow_run)
                else:
                    # TODO: Implement update method in repository if needed
                    pass
            except:
                pass  # If we can't save the failed state, just continue with the original error
            raise e 