from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime

class AppCredentialBase(BaseModel):
    """Base model for app credential data"""
    user_id: str = Field(..., description="ID of the user who owns these credentials")
    app_id: str = Field(..., description="ID of the app these credentials are for")
    values: Dict[str, str] = Field(
        default_factory=dict,
        description="Key-value pairs of credential/connection values"
    )
    workflow_ids: Optional[List[str]] = Field(
        default_factory=list,
        description="List of workflow IDs that use these credentials"
    )

class AppCredentialCreate(AppCredentialBase):
    """Model for creating app credentials"""
    pass

class AppCredentialUpdate(BaseModel):
    """Model for updating app credentials"""
    values: Optional[Dict[str, str]] = None
    workflow_ids: Optional[List[str]] = None

class AppCredentialResponse(AppCredentialBase):
    """Model for app credential response"""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        json_schema_extra = {
            "example": {
                "id": "6803bf0da1bb6891ee827df7",
                "user_id": "user123",
                "app_id": "whatsapp",
                "values": {
                    "ACCESS_TOKEN": "token123",
                    "REFRESH_TOKEN": "refresh123"
                },
                "workflow_ids": ["workflow1", "workflow2"],
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
