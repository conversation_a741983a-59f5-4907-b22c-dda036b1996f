from pydantic import Field, field_validator, model_validator
from typing import Optional, Dict, Any, List
from datetime import datetime, UTC
from app.core.data import BaseDBModel
from app.core.data.mongodb_utils import PyObjectId
from app.models.app import AppCredential

class Workflow(BaseDBModel):
    """Domain model for workflow business logic"""
    id: Optional[PyObjectId] = Field(default=None, alias="_id")
    user_id: Optional[str] = Field(None, description="ID of the user who created the workflow")
    prompt: str = ""  # Default to empty string
    status: str = "setup"  # Can be "setup", "ready", "running", "completed", "failed"
    original_user_input: str = ""
    original_user_input_language: str = ""
    user_input_english_text: str = ""
    timing: Dict[str, Any] = Field(default_factory=dict)
    cleaned_task_prompt: str = ""
    suggested_apps: List[Dict[str, Any]] = Field(default_factory=list)
    apps: List[str] = Field(
        default_factory=list,
        description="List of app IDs that user confirmed to use"
    )
    agent_instructions: Optional[str] = None
    errors: List[str] = Field(default_factory=list)
    eligibility: Dict[str, Any] = Field(default_factory=dict)
    sharing_enabled: bool = False

    @field_validator('status')
    def validate_status(cls, v):
        valid_statuses = ["setup", "ready", "running", "completed", "failed"]
        if v not in valid_statuses:
            raise ValueError(f"Status must be one of {valid_statuses}")
        return v

    @model_validator(mode='after')
    def ensure_data_consistency(self):
        """Ensure data consistency between fields"""
        # If original_user_input is empty but prompt is set, copy prompt to original_user_input
        if not self.original_user_input and self.prompt:
            self.original_user_input = self.prompt

        # If prompt is empty but original_user_input is set, copy original_user_input to prompt
        if not self.prompt and self.original_user_input:
            self.prompt = self.original_user_input

        return self

    def mark_as_failed(self, error: str = None):
        """Mark workflow as failed with optional error"""
        self.status = "failed"
        if error:
            self.errors.append(error)
        self.update_modified_at()
