from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List
from datetime import datetime
from app.models.app import AppCredential

# Base DTOs
class WorkflowDTO(BaseModel):
    """Base DTO for workflow operations"""
    pass

# Input DTOs
class WorkflowCreateDTO(WorkflowDTO):
    """DTO for creating a new workflow"""
    prompt: str

class WorkflowUpdateDTO(WorkflowDTO):
    """DTO for updating a workflow"""
    prompt: Optional[str] = None
    app_id: Optional[str] = None
    status: Optional[str] = None

class WorkflowConfirmDTO(WorkflowDTO):
    """DTO for confirming a workflow with app configurations"""
    workflow_id: str
    apps: List[AppCredential] = Field(
        ...,
        description="List of apps with their credentials that user wants to use"
    )
    share: bool = False

class WorkflowScheduleDTO(WorkflowDTO):
    """DTO for scheduling a new workflow"""
    workflow_id: str

class StartWorkflowRunDTO(BaseModel):
    """DTO for starting a workflow run"""
    workflow_id: str
    workflow_run_id: str

# Output DTOs
class WorkflowResponseDTO(WorkflowDTO):
    """DTO for workflow API responses"""
    id: str
    status: str
    created_at: datetime
    original_user_input: str = ""
    original_user_input_language: str = ""
    user_input_english_text: str = ""
    timing: Dict[str, Any] = Field(default_factory=dict)
    cleaned_task_prompt: str = ""
    sharing_enabled: bool = False
    prompt: str = ""

class WorkflowListResponseDTO(WorkflowDTO):
    """DTO for listing workflows"""
    workflows: List[WorkflowResponseDTO]
    total: int

class APIResponseDTO(BaseModel):
    """Standard API response"""
    workflow_id: str
    message: str

class WorkflowStartResponseDTO(BaseModel):
    """Response DTO for workflow start endpoint"""
    status: str
    workflow_id: str
    timing: Dict[str, Any] = Field(default_factory=dict)
    suggested_apps: List[Dict[str, Any]] = Field(default_factory=list)