from datetime import datetime
from enum import Enum
from typing import Optional
from pydantic import Field
from uuid import UUID
from app.core.data import BaseDBModel
from app.core.data.mongodb_utils import PyObjectId

class WorkflowStatus(str, Enum):
    PENDING = "pending"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"

class WorkflowRun(BaseDBModel):
    """Domain model for workflow run instances"""
    id: Optional[PyObjectId] = Field(default=None, alias="_id")
    workflow_id: str
    status: WorkflowStatus = Field(default=WorkflowStatus.PENDING)
    scheduled_time: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "workflow_id": "123e4567-e89b-12d3-a456-************",
                "status": "pending",
                "scheduled_time": "2024-03-21T10:00:00Z"
            }
        } 