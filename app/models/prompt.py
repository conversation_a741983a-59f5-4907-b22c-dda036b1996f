from pydantic import BaseModel, Field
from datetime import datetime

class PromptCreate(BaseModel):
    name: str
    content: str
    description: str = ""

class PromptUpdate(BaseModel):
    name: str | None = None
    content: str | None = None
    description: str | None = None

class PromptResponse(BaseModel):
    id: str
    name: str
    content: str
    description: str
    created_at: datetime
    updated_at: datetime 