from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional
from enum import Enum
from app.models.app import TransportType

class McpServerConfig(BaseModel):
    """Response DTO for app configuration"""
    id: str = Field(..., description="ID of the app")
    name: str = Field(..., description="Name of the app")
    transport: TransportType = Field(..., description="Transport type for the app")
    runtime: str = Field(..., description="Runtime command for the app")
    args: List[str] = Field(..., description="Arguments for the app")
    env: Dict[str, str] = Field(..., description="Environment variables for the app")
    git_url: Optional[str] = Field(None, description="Git URL for Docker apps")
    docker_file_path: Optional[str] = Field(None, description="Docker file path for Docker apps")

class AgentRunnerStatusResponse(str, Enum):
    """Type of application"""
    READY = "ready"  # MCP server applications
    CANCELED = "canceled"              # Tool applications

class AgentRunnerResponse(BaseModel):
    """Response model for agent runner"""
    status: Optional[AgentRunnerStatusResponse] = Field(None, description="Status of the workflow run")
    message: Optional[str] = Field(None, description="Message of the workflow run status")
    mcp_servers: List[McpServerConfig]
    prompt: str
    agent_instructions: str
    user_id: Optional[str] = None