from pydantic import BaseModel, EmailStr, Field
from typing import List
from datetime import datetime, UTC

class UserBase(BaseModel):
    name: str
    email: EmailStr

class UserCreate(UserBase):
    pass

class UserInDB(UserBase):
    id: str = Field(alias="_id")
    roles: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))

class UserResponse(UserBase):
    id: str
    roles: List[str]
    created_at: datetime
    updated_at: datetime 