from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional

class APIKeyBase(BaseModel):
    user_id: str
    is_active: bool = True

class APIKeyCreate(APIKeyBase):
    pass

class APIKeyInDB(APIKeyBase):
    id: str = Field(alias="_id")
    key: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_used_at: Optional[datetime] = None

class APIKeyResponse(APIKeyBase):
    id: str
    key: str
    created_at: datetime
    last_used_at: Optional[datetime] = None 