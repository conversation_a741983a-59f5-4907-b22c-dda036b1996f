from pydantic import BaseModel, <PERSON>
from typing import List, Dict
from datetime import datetime

class BlogPostBase(BaseModel):
    """Base model for blog post data"""
    title: str
    description: str
    content: str
    date: str
    reading_time: int = Field(..., description="Reading time in minutes")
    author_name: str
    author_title: str
    author_image_url: str
    image: str
    tags: List[str]
    slugs: Dict[str, str] = Field(
        default_factory=lambda: {"en": ""},
        description="URL slugs for different locales. Key is locale code, value is slug"
    )

class BlogPostCreate(BlogPostBase):
    """Model for creating a blog post"""
    pass

class BlogPostResponse(BlogPostBase):
    """Model for blog post response"""
    id: str
    created_at: datetime
    updated_at: datetime

class ImageUploadResponse(BaseModel):
    """Model for image upload response"""
    image_id: str
    image_url: str
    blog_post_id: str 