from pydantic import Field
from typing import List, Dict, Optional
from app.core.data import BaseDBModel
from app.core.data.mongodb_utils import PyObjectId

class AppCredential(BaseDBModel):
    """Domain model for app credentials business logic"""
    id: PyObjectId = Field(default=None, alias="_id")
    user_id: str = Field(..., description="ID of the user who owns these credentials")
    app_id: str = Field(..., description="ID of the app these credentials are for")
    values: Dict[str, str] = Field(
        default_factory=dict,
        description="Key-value pairs of credential/connection values"
    )
    workflow_ids: List[str] = Field(
        default_factory=list,
        description="List of workflow IDs that use these credentials"
    )
