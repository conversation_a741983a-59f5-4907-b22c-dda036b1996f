from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Literal
from datetime import datetime, UTC
from enum import Enum

class VariableType(str, Enum):
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"

class VariableLocation(str, Enum):
    ENV = "env"
    ARG = "arg"

class AuthMethod(str, Enum):
    DIRECT = "direct"
    OAUTH = "oauth"

class OAuthProvider(str, Enum):
    GMAIL = "gmail"
    GOOGLE_DRIVE = "google-drive"
    GOOGLE_CALENDAR = "google-calendar"
    OUTLOOK = "outlook"
    ONEDRIVE = "onedrive"

class OAuthProviderConfig(BaseModel):
    """Configuration for OAuth provider"""
    type: OAuthProvider = Field(..., description="Type of OAuth provider")
    scopes: List[str] = Field(..., description="Required OAuth scopes")
    additional_settings: Dict[str, str] = Field(
        default_factory=dict,
        description="Additional provider-specific settings"
    )

class AuthVariableDefinition(BaseModel):
    """Defines a variable required for app authentication"""
    name: str = Field(..., description="Name of the variable")
    type: VariableType = Field(..., description="Type of the variable")
    location: VariableLocation = Field(
        default=VariableLocation.ARG,
        description="Whether this variable should be passed as an environment variable or argument"
    )
    description: str = Field("", description="Description of what this variable is for")
    required: bool = Field(True, description="Whether this variable is required")
    default: Optional[str] = Field(None, description="Default value if any")
    arg_prefix: Optional[str] = Field(None, description="Prefix for argument if needed (e.g., '--id-instance')")
    arg_format: Optional[Literal["space", "equals"]] = Field(
        description="How to format the argument: 'space' for '--key value', 'equals' for '--key=value'"
    )

class VariableValue(BaseModel):
    """Represents a value for an auth variable"""
    name: str = Field(..., description="Name of the variable")
    value: str = Field(..., description="Value of the variable")

class AppCredential(BaseModel):
    """Represents the credentials/variables for an app instance"""
    app_id: str = Field(..., description="ID of the app these credentials are for")
    variables: List[VariableValue] = Field(
        default_factory=list,
        description="List of variable values for this app"
    )

class AppType(str, Enum):
    """Type of application"""
    MCP_SERVER = "mcp_server"  # MCP server applications
    TOOL = "tool"              # Tool applications

class TransportType(str, Enum):
    """Transport mechanism for the application"""
    STDIO = "stdio"            # Standard I/O transport
    SSE = "sse"                # Server-Sent Events transport
    WS = "ws"                  # WebSocket transport
    AGNO_TOOL = "agno_tool"    # Agno tool transport

class RuntimeEnvironment(str, Enum):
    """Runtime environment for the application"""
    LOCAL = "local"            # Run locally with commands like npx/uvx
    DOCKER = "docker"          # Run in Docker container

class AppBase(BaseModel):
    name: str
    tags: List[str]
    description: str
    app_type: AppType = Field(
        default=AppType.MCP_SERVER,
        description="Type of the application (MCP server or tool)"
    )
    transport: TransportType
    runtime_env: RuntimeEnvironment = Field(
        default=RuntimeEnvironment.LOCAL,
        description="Runtime environment for the application"
    )
    instructions: str = Field(
        "",
        description="HTML-formatted instructions for app authentication"
    )
    auth_variables: List[AuthVariableDefinition] = Field(
        default_factory=list,
        description="List of variables required for app authentication"
    )
    auth_method: AuthMethod = Field(
        default=AuthMethod.DIRECT,
        description="Authentication method for this variable"
    )
    oauth_provider: Optional[OAuthProviderConfig] = Field(
        description="OAuth provider configuration if the app uses OAuth"
    )
    command: str
    args: List[str]
    git_url: Optional[str] = Field(
        description="Git repository URL for Docker-based apps"
    )
    docker_file_path: Optional[str] = Field(
        description="Path to Dockerfile within the git repository"
    )

class AppCreate(AppBase):
    pass

class AppInDB(AppBase):
    id: str = Field(alias="_id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))

class AppResponse(AppBase):
    id: str
    created_at: datetime
    updated_at: datetime

"""
Example of how the Gmail app should be stored in MongoDB:

{
    "_id": {"$oid": "6803bf0da1bb6891ee827df5"},
    "created_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "updated_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "name": "gmail",
    "tags": ["gmail", "email"],
    "description": "Gmail Server for sending emails via Gmail API",
    "app_type": "mcp_server",
    "transport": "stdio",
    "runtime_env": "local",
    "instructions": "Requires Google OAuth authentication with Gmail API access",
    "command": "npx",
    "args": ["-y", "@morrowai/mcp-server-gmail"],
    "auth_variables": [
        {
            "name": "ACCESS_TOKEN",
            "type": "string",
            "location": "env",
            "description": "Google OAuth access token for Gmail API",
            "required": true,
            "auth_method": "oauth",
            "provider": {
                "type": "gmail",
                "scopes": ["https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/gmail.readonly"],
                "additional_settings": {
                    "access_type": "offline",
                    "prompt": "consent"
                }
            }
        }
    ],
    "oauth_provider": {
        "type": "gmail",
        "scopes": ["https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/gmail.readonly"],
        "additional_settings": {
            "access_type": "offline",
            "prompt": "consent"
        }
    }
}

When a user confirms the workflow, they would provide credentials like:

{
    "app_id": "6803bf0da1bb6891ee827df5",
    "variables": [
        {
            "name": "ACCESS_TOKEN",
            "value": "actual-oauth-token"
        }
    ]
}

Example of how a Docker-based app should be stored in MongoDB:

{
    "_id": {"$oid": "6803bf0da1bb6891ee827df6"},
    "created_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "updated_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "name": "custom-docker-app",
    "tags": ["docker", "custom"],
    "description": "Custom Docker-based application",
    "app_type": "mcp_server",
    "transport": "stdio",
    "runtime_env": "docker",
    "instructions": "Requires Docker to be installed and running",
    "command": "docker",
    "args": ["run", "--rm"],
    "git_url": "https://github.com/username/repo.git",
    "docker_file_path": "Dockerfile",
    "auth_variables": [
        {
            "name": "API_KEY",
            "type": "string",
            "location": "env",
            "description": "API key for the service",
            "required": true
        }
    ],
    "oauth_provider": null
}

Example of a Tool app with agno-tool transport:

{
    "_id": {"$oid": "6803bf0da1bb6891ee827df7"},
    "created_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "updated_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "name": "agno-tool-app",
    "tags": ["tool", "utility"],
    "description": "Tool application using agno-tool transport",
    "app_type": "tool",
    "transport": "agno_tool",
    "runtime_env": "local",
    "instructions": "Tool for processing data",
    "command": "npx",
    "args": ["-y", "@morrowai/agno-tool-processor"],
    "auth_variables": [
        {
            "name": "API_KEY",
            "type": "string",
            "location": "env",
            "description": "API key for the service",
            "required": true
        }
    ],
    "oauth_provider": null
}

Example of an MCP server with SSE transport:

{
    "_id": {"$oid": "6803bf0da1bb6891ee827df8"},
    "created_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "updated_at": {"$date": "2025-04-19T15:19:41.809Z"},
    "name": "sse-server",
    "tags": ["sse", "realtime"],
    "description": "MCP server using SSE transport",
    "app_type": "mcp_server",
    "transport": "sse",
    "runtime_env": "local",
    "instructions": "Server with real-time updates",
    "command": "npx",
    "args": ["-y", "@morrowai/mcp-server-sse"],
    "auth_variables": [
        {
            "name": "API_KEY",
            "type": "string",
            "location": "env",
            "description": "API key for the service",
            "required": true
        }
    ],
    "oauth_provider": null
}
"""