from pydantic import Field
from typing import List, Dict
from app.core.data import BaseDBModel
from app.core.data.mongodb_utils import PyObjectId

class BlogPost(BaseDBModel):
    """Domain model for blog post business logic"""
    id: PyObjectId = Field(default=None, alias="_id")
    title: str
    description: str
    content: str
    date: str
    reading_time: int  # in minutes
    author_name: str
    author_title: str
    author_image_url: str
    image: str
    tags: List[str]
    slugs: Dict[str, str] = Field(
        default_factory=lambda: {"en": ""},
        description="URL slugs for different locales. Key is locale code, value is slug"
    ) 