from pydantic import Field
from typing import List, Dict, Optional
from app.core.data import BaseDBModel
from app.core.data.mongodb_utils import PyObjectId
from app.models.app import AuthVariableDefinition, TransportType, AuthMethod, OAuthProviderConfig, AppType, RuntimeEnvironment

class App(BaseDBModel):
    """Domain model for app business logic"""
    id: PyObjectId = Field(default=None, alias="_id")
    name: str
    tags: List[str]
    description: str
    app_type: AppType = Field(
        default=AppType.MCP_SERVER,
        description="Type of the application (MCP server or tool)"
    )
    transport: TransportType
    runtime_env: RuntimeEnvironment = Field(
        default=RuntimeEnvironment.LOCAL,
        description="Runtime environment for the application"
    )
    instructions: str = Field(
        default="",
        description="HTML-formatted instructions for app authentication"
    )
    auth_variables: List[AuthVariableDefinition] = Field(
        default_factory=list,
        description="List of variables required for app authentication"
    )
    auth_method: AuthMethod = Field(
        default=AuthMethod.DIRECT,
        description="Authentication method for this variable"
    )
    oauth_provider: Optional[OAuthProviderConfig] = Field(
        description="OAuth provider configuration if the app uses OAuth"
    )
    command: str
    args: List[str]
    git_url: Optional[str] = Field(
        description="Git repository URL for Docker-based apps"
    )
    docker_file_path: Optional[str] = Field(
        description="Path to Dockerfile within the git repository"
    )