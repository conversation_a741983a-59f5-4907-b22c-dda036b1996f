from motor.motor_asyncio import AsyncIOMotorClient
from app.config.settings_loader import get_mongodb_settings
import asyncio
import logging

class MongoDB:
    client: AsyncIOMotorClient = None
    db = None

    @classmethod
    async def connect_db(cls):
        """Connect to MongoDB"""
        if cls.client is None:
            try:
                settings = get_mongodb_settings()
                print(f"Connecting to MongoDB at {settings.host}...")
                
                # Add server selection timeout
                cls.client = AsyncIOMotorClient(
                    settings.mongodb_url,
                    serverSelectionTimeoutMS=5000,  # 5 second timeout
                    connectTimeoutMS=5000,
                    socketTimeoutMS=5000
                )
                
                # Test the connection
                await cls.client.admin.command('ping')
                
                cls.db = cls.client[settings.mongodb_db_name]
                print("Successfully connected to MongoDB!")
                
            except Exception as e:
                print(f"Failed to connect to MongoDB: {str(e)}")
                if cls.client:
                    cls.client.close()
                    cls.client = None
                raise

    @classmethod
    async def close_db(cls):
        """Close MongoDB connection"""
        if cls.client is not None:
            try:
                cls.client.close()
                print("MongoDB connection closed successfully")
            except Exception as e:
                print(f"Error closing MongoDB connection: {str(e)}")
            finally:
                cls.client = None
                cls.db = None

    @classmethod
    def get_db(cls):
        """Get database instance"""
        if cls.db is None:
            raise RuntimeError("Database not initialized. Call connect_db first.")
        return cls.db 