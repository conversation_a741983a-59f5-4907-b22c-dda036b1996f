from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel

from app.models.workflow_dto import (
    WorkflowCreateDTO, 
    WorkflowUpdateDTO, 
    WorkflowConfirmDTO,
    WorkflowResponseDTO, 
    WorkflowListResponseDTO,
    APIResponseDTO,
    WorkflowScheduleDTO,
    WorkflowStartResponseDTO
)
from app.core.auth import get_admin_user
from app.core.dependencies import (
    WorkflowServiceDependency,
    WorkflowRunRepositoryDependency
)
from app.models.workflow_run import WorkflowRun
from app.services.task_scheduler_service import TaskSchedulerService
from app.services.logging_service import LoggingService

router = APIRouter()
logging_service = LoggingService()

@router.get("", response_model=WorkflowListResponseDTO)
async def list_workflows(
    service: WorkflowServiceDependency,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    api_key: dict = Depends(get_admin_user)
):
    """List workflows with pagination"""
    workflows, total = await service.list_workflows(skip, limit)
    return WorkflowListResponseDTO(
        workflows=workflows,
        total=total
    )

@router.get("/{workflow_id}", response_model=WorkflowStartResponseDTO)
async def get_workflow(
    workflow_id: str,
    service: WorkflowServiceDependency,
    user: dict = Depends(get_admin_user)
):
    """Get workflow by ID (with up-to-date suggested_apps)"""
    user_id = str(user["_id"])
    workflow = await service.get_workflow_by_id(workflow_id, user_id)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return workflow

@router.put("/{workflow_id}", response_model=WorkflowResponseDTO)
async def update_workflow(
    workflow_id: str,
    workflow: WorkflowUpdateDTO,
    service: WorkflowServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Update workflow"""
    return await service.update_workflow(workflow_id, workflow)

@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    service: WorkflowServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Delete workflow"""
    success = await service.delete_workflow(workflow_id)
    if not success:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return {"message": "Workflow deleted successfully"}

@router.post("/start", response_model=WorkflowStartResponseDTO, 
             summary="Start a new workflow",
             description="""
             Initiates a new workflow based on the user's prompt. This endpoint performs the following steps:
             
             1. Eligibility Check: Validates if the prompt is eligible for processing
             2. Translation: Translates non-English prompts to English
             3. Timing Detection: Identifies scheduling information in the prompt
             4. Task Cleaning: Removes timing information to focus on the core task
             5. App Detection: Identifies which apps are needed for the task
             
             The workflow is created with status "setup" and requires confirmation before execution.
             """)
async def start_workflow(
    request: WorkflowCreateDTO,
    service: WorkflowServiceDependency,
    user: dict = Depends(get_admin_user)
):
    """Start a new workflow"""
    return await service.create_workflow(request, str(user["_id"]))

@router.post("/confirm", response_model=APIResponseDTO,
             summary="Confirm a workflow",
             description="""
             Confirms and finalizes a workflow that was previously created with the start endpoint.
             This endpoint allows the user to:
             
             1. Modify the detected apps (add/remove apps)
             2. Set variables for the apps (e.g., API keys, credentials)
             3. Configure sharing settings
             
             After confirmation, the workflow status changes to "ready" and it can be executed.
             
             The workflow_id is required and must be obtained from the start endpoint response.
             """)
async def confirm_workflow(
    modifications: WorkflowConfirmDTO,
    service: WorkflowServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Confirm a workflow"""
    return await service.confirm_workflow(modifications)

@router.post("/schedule", response_model=WorkflowRun)
async def schedule_workflow(
    workflow_schedule: WorkflowScheduleDTO,
    workflow_run_repository: WorkflowRunRepositoryDependency,
    api_key: dict = Depends(get_admin_user)
) -> WorkflowRun:
    """
    Schedule a workflow for execution.
    Creates a workflow run record and schedules it in Cloud Tasks.
    """
    scheduler = TaskSchedulerService(workflow_run_repository)
    try:
        workflow_run = await scheduler.schedule_workflow(workflow_schedule.workflow_id)
        # Log successful scheduling
        await logging_service.log_message(
            workflow_run_id=str(workflow_run.id),
            message="Workflow run scheduled"
        )
        return workflow_run
    except Exception as e:
        # Log failed scheduling
        await logging_service.log_message(
            workflow_run_id=workflow_schedule.workflow_id,
            message="Workflow run failed to schedule",
            severity="ERROR"
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to schedule workflow: {str(e)}"
        )