from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends, Request
from app.core.auth import get_api_key
from app.core.dependencies import get_oauth_service
from app.services.oauth_service import OAuthService
from pydantic import BaseModel
from typing import Dict, Optional
import logging
from fastapi.responses import HTMLResponse

# Set up logging
logger = logging.getLogger(__name__)

router = APIRouter()

class OAuthInitiateRequest(BaseModel):
    workflow_id: str
    app_id: str
    variable_name: str
    provider: str
    redirect_to: Optional[str] = None

class RefreshTokenRequest(BaseModel):
    app_id: str
    provider: str

@router.post("/initiate", response_model=Dict)
async def initiate_oauth(
    request: OAuthInitiateRequest,
    api_key_doc: dict = Depends(get_api_key),
    service: OAuthService = Depends(get_oauth_service)
):
    """Initiate OAuth flow for a specific app variable"""
    user_id = api_key_doc["user_id"]
    return await service.initiate_oauth(
        user_id,
        request.workflow_id,
        request.app_id,
        request.variable_name,
        request.provider,
        request.redirect_to
    )

@router.get("/callback")
async def oauth_callback(
    request: Request,
    service: OAuthService = Depends(get_oauth_service)
):
    """Handle OAuth callback"""
    params = dict(request.query_params)
    code = params.get("code")
    state = params.get("state")
    error = params.get("error")

    logger.info(f"Received OAuth callback with state: {state[:5] if state else 'None'}...")
    logger.info(f"Query parameters: {params}")

    try:
        if not state:
            logger.error("No state parameter provided in OAuth callback")
            raise HTTPException(status_code=400, detail="Missing state parameter")

        # Get the OAuth state from the database
        oauth_state = await service.db.oauth_states.find_one({"state": state})

        if not oauth_state:
            logger.error(f"OAuth state not found in database: {state[:5] if state else 'None'}...")
            # List recent states for debugging
            recent_states = await service.db.oauth_states.find().sort("created_at", -1).limit(5).to_list(length=5)
            if recent_states:
                logger.info(f"Recent states in database: {[s['state'][:5] + '...' for s in recent_states]}")
            else:
                logger.info("No recent OAuth states found in database")

            raise HTTPException(status_code=400, detail="Invalid or expired OAuth state")

        logger.info(f"Found OAuth state in database for app_id: {oauth_state.get('app_id')}, variable: {oauth_state.get('variable_name')}")
        redirect_to = oauth_state.get("redirect_to")
        app_id = oauth_state.get("app_id", "")
        variable_name = oauth_state.get("variable_name", "")

        # Handle OAuth error from provider
        if error:
            logger.error(f"OAuth provider returned error: {error}")
            # Clean up state
            await service.db.oauth_states.delete_one({"state": state})
            raise HTTPException(status_code=400, detail=f"OAuth provider error: {error}")

        if not code:
            logger.error("No code parameter provided in OAuth callback")
            raise HTTPException(status_code=400, detail="Missing code parameter")

        # Process the OAuth callback
        result = await service.handle_oauth_callback(code, state)

        # Get the app_id and variable_name from the result
        app_id = result.get("app_id", "") or app_id
        workflow_id = result.get("workflow_id", "") or workflow_id
        variable_name = result.get("variable", "") or variable_name

        # Determine the redirect URL based on the redirect_to parameter
        frontend_url = service.oauth_settings.frontend_url

        # Build the redirect URL with success status and variable name
        if redirect_to == "workflow_wizard":
            redirect_url = f"{frontend_url}/workflows/new?workflow_id={workflow_id}&oauth_success=true&app_id={app_id}&variable_name={variable_name}"
        else:
            # Default to the apps page if no specific redirect is provided
            redirect_url = f"{frontend_url}/apps/{app_id}?oauth_success=true&variable_name={variable_name}"

    except HTTPException as e:
        # Handle errors by redirecting to the frontend with error information
        frontend_url = service.oauth_settings.frontend_url
        error_message = e.detail.replace(" ", "+")  # URL-encode spaces

        if oauth_state:
            app_id = oauth_state.get("app_id", "")
            variable_name = oauth_state.get("variable_name", "")
            redirect_to = oauth_state.get("redirect_to")

            if redirect_to == "workflow_wizard":
                redirect_url = f"{frontend_url}/workflows/new?oauth_error={error_message}&app_id={app_id}&variable_name={variable_name}"
            else:
                redirect_url = f"{frontend_url}/apps/{app_id}?oauth_error={error_message}&variable_name={variable_name}"
        else:
            # Fallback if we don't have oauth_state
            redirect_url = f"{frontend_url}/apps?oauth_error={error_message}"

    # Always redirect to the frontend
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>OAuth Complete</title>
        <meta http-equiv="refresh" content="0;url={redirect_url}">
        <script>
            window.location.href = "{redirect_url}";
        </script>
    </head>
    <body>
        <h1>Authentication Complete</h1>
        <p>Redirecting back to application...</p>
        <p>If you are not redirected automatically, <a href="{redirect_url}">click here</a>.</p>
    </body>
    </html>
    """)

@router.get("/status/{app_id}", response_model=Dict[str, str])
async def check_oauth_status(
    app_id: str,
    api_key_doc: dict = Depends(get_api_key),
    service: OAuthService = Depends(get_oauth_service)
):
    """Check OAuth status for all variables of an app"""
    user_id = api_key_doc["user_id"]
    return await service.check_oauth_status(user_id, app_id)

@router.post("/refresh", response_model=Dict)
async def refresh_oauth_token(
    request_data: RefreshTokenRequest,
    api_key_doc: dict = Depends(get_api_key),
    service: OAuthService = Depends(get_oauth_service)
):
    """Refresh OAuth access token for a specific app and provider"""
    user_id = api_key_doc["user_id"]
    logger.info(f"Attempting to refresh token for user: {user_id}, app: {request_data.app_id}, provider: {request_data.provider}")
    try:
        result = await service.refresh_access_token(
            user_id=user_id,
            app_id=request_data.app_id,
            provider=request_data.provider
        )
        return result
    except HTTPException as e:
        logger.error(f"Error refreshing token for user {user_id}, app {request_data.app_id}: {e.detail}")
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error refreshing token for user {user_id}, app {request_data.app_id}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred while refreshing the token.")