from fastapi import APIRouter, HTTPException
from app.models.user import UserCreate, UserResponse
from app.database.mongodb import MongoDB
from app.core.auth import generate_api_key
from datetime import datetime, UTC
from bson import ObjectId

router = APIRouter()

# Whitelist of allowed email addresses for registration
ALLOWED_EMAILS = [
    "<EMAIL>",
    "<EMAIL>"
]

@router.post("/register")
async def register_user(user: UserCreate):
    # Check if the email is in the whitelist
    if user.email not in ALLOWED_EMAILS:
        raise HTTPException(
            status_code=403,
            detail="Registration is not authorized for this email address"
        )
    
    db = MongoDB.get_db()
    
    # Check if user already exists
    existing_user = await db.users.find_one({"email": user.email})
    if existing_user:
        # Find the user's active API key
        user_id = str(existing_user["_id"])
        api_key_doc = await db.api_keys.find_one({"user_id": user_id, "is_active": True})
        
        if api_key_doc:
            return {"api_key": api_key_doc["key"]}
        else:
            # If no active API key exists, generate a new one
            api_key = generate_api_key()
            api_key_data = {
                "user_id": user_id,
                "key": api_key,
                "is_active": True,
                "created_at": datetime.now(UTC)
            }
            await db.api_keys.insert_one(api_key_data)
            return {"api_key": api_key}
    
    # Create user with empty roles
    user_data = user.model_dump()
    user_data["roles"] = []
    user_data["created_at"] = datetime.now(UTC)
    user_data["updated_at"] = datetime.now(UTC)
    
    result = await db.users.insert_one(user_data)
    user_id = str(result.inserted_id)
    
    # Generate API key
    api_key = generate_api_key()
    api_key_data = {
        "user_id": user_id,
        "key": api_key,
        "is_active": True,
        "created_at": datetime.now(UTC)
    }
    
    await db.api_keys.insert_one(api_key_data)
    
    return {"api_key": api_key} 