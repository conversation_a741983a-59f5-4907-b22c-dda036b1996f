from fastapi import APIRouter, Depends, HTTPException
from typing import List
from app.models.prompt import PromptCreate, PromptUpdate, PromptResponse
from app.services.prompt_service import PromptService
from app.core.dependencies import get_prompt_service
from app.core.auth import get_admin_user

router = APIRouter()

@router.post("", response_model=PromptResponse)
async def create_prompt(
    prompt: PromptCreate,
    _: dict = Depends(get_admin_user),
    service: PromptService = Depends(get_prompt_service)
):
    return await service.create_prompt(prompt)

@router.get("", response_model=List[PromptResponse])
async def list_prompts(
    skip: int = 0,
    limit: int = 10,
    service: PromptService = Depends(get_prompt_service)
):
    return await service.list_prompts(skip, limit)

@router.get("/{prompt_id}", response_model=PromptResponse)
async def get_prompt(
    prompt_id: str,
    service: PromptService = Depends(get_prompt_service)
):
    return await service.get_prompt(prompt_id)

@router.put("/{prompt_id}", response_model=PromptResponse)
async def update_prompt(
    prompt_id: str,
    prompt: PromptUpdate,
    _: dict = Depends(get_admin_user),
    service: PromptService = Depends(get_prompt_service)
):
    return await service.update_prompt(prompt_id, prompt)

@router.delete("/{prompt_id}")
async def delete_prompt(
    prompt_id: str,
    _: dict = Depends(get_admin_user),
    service: PromptService = Depends(get_prompt_service)
):
    success = await service.delete_prompt(prompt_id)
    if not success:
        raise HTTPException(404, "Prompt not found")
    return {"message": "Prompt deleted successfully"} 