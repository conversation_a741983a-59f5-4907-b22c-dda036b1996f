from fastapi import APIRouter, HTTPException, Depends
from app.models.app import AppCreate, AppResponse
from app.core.auth import get_admin_user
from app.core.dependencies import get_app_service
from app.services.app_service import AppService
from typing import List

router = APIRouter()

@router.post("", response_model=AppResponse)
async def create_app(
    app: AppCreate,
    _: dict = Depends(get_admin_user),
    service: AppService = Depends(get_app_service)
):
    """Create a new app"""
    return await service.create_app(app)

@router.get("", response_model=List[AppResponse])
async def list_apps(
    _: dict = Depends(get_admin_user),
    service: AppService = Depends(get_app_service)
):
    """List all apps"""
    return await service.list_apps()

@router.get("/{app_id}", response_model=AppResponse)
async def get_app(
    app_id: str,
    _: dict = Depends(get_admin_user),
    service: AppService = Depends(get_app_service)
):
    """Get app by ID"""
    app = await service.get_app_by_id(app_id)
    if not app:
        raise HTTPException(status_code=404, detail="App not found")
    return app

@router.put("/{app_id}", response_model=AppResponse)
async def update_app(
    app_id: str,
    app: AppCreate,
    _: dict = Depends(get_admin_user),
    service: AppService = Depends(get_app_service)
):
    """Update app"""
    return await service.update_app(app_id, app)

@router.delete("/{app_id}")
async def delete_app(
    app_id: str,
    _: dict = Depends(get_admin_user),
    service: AppService = Depends(get_app_service)
):
    """Delete app"""
    success = await service.delete_app(app_id)
    if not success:
        raise HTTPException(status_code=404, detail="App not found")
    return {"message": "App deleted successfully"} 