from fastapi import APIRouter, HTTP<PERSON>x<PERSON>, Depends, Query, Head<PERSON>
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel

from app.models.workflow_run import WorkflowRun, WorkflowStatus
from app.models.workflow_dto import StartWorkflowRunDTO
from app.core.auth import get_admin_user
from app.core.dependencies import (
    WorkflowRunServiceDependency,
    WorkflowServiceDependency,
    AppServiceDependency
)
from app.services.logging_service import logging_service
from app.models.agent_runner import AgentRunnerResponse, AgentRunnerStatusResponse
router = APIRouter()

# Hardcoded API key for agent-runner service
AGENT_RUNNER_API_KEY = "2351b3a7-bb30-4024-a763-acca3e917cff"

async def verify_agent_runner_api_key(x_api_key: str = Header(...)):
    """Verify the API key for agent-runner service"""
    if x_api_key != AGENT_RUNNER_API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API key"
        )
    return x_api_key

class WorkflowRunListResponse(BaseModel):
    """Response model for list workflow runs"""
    data: List[WorkflowRun]
    total: int

class WorkflowRunCreateRequest(BaseModel):
    """Request model for creating a workflow run"""
    workflow_id: str

class WorkflowRunUpdateRequest(BaseModel):
    """Request model for updating a workflow run status"""
    status: WorkflowStatus
    error_message: Optional[str] = None

class WorkflowLogsResponse(BaseModel):
    """Response model for workflow logs"""
    data: List[Dict[str, Any]]
    total: int

class WorkflowLogsRequest(BaseModel):
    """Request model for getting workflow logs"""
    workflow_run_id: str
    skip: int = 0
    limit: int = 100

@router.get("", response_model=WorkflowRunListResponse)
async def list_workflow_runs(
    service: WorkflowRunServiceDependency,
    workflow_id: Optional[str] = None,
    status: Optional[WorkflowStatus] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    api_key: dict = Depends(get_admin_user)
):
    """List workflow runs with optional filtering"""
    runs, total = await service.list_workflow_runs(workflow_id, status, skip, limit)
    return WorkflowRunListResponse(data=runs, total=total)

@router.get("/{run_id}", response_model=WorkflowRun)
async def get_workflow_run(
    run_id: str,
    service: WorkflowRunServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Get a workflow run by ID"""
    run = await service.get_workflow_run(run_id)
    if not run:
        raise HTTPException(status_code=404, detail="Workflow run not found")
    return run

@router.post("", response_model=WorkflowRun)
async def create_workflow_run(
    request: WorkflowRunCreateRequest,
    service: WorkflowRunServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Create a new workflow run"""
    return await service.create_workflow_run(request.workflow_id)

@router.put("/{run_id}/status", response_model=WorkflowRun)
async def update_workflow_run_status(
    run_id: str,
    request: WorkflowRunUpdateRequest,
    service: WorkflowRunServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Update workflow run status"""
    success = await service.update_workflow_run_status(
        run_id,
        request.status,
        request.error_message
    )
    if success:
        return await service.get_workflow_run(run_id)
    raise HTTPException(status_code=404, detail="Workflow run not found")

@router.delete("/{run_id}")
async def delete_workflow_run(
    run_id: str,
    service: WorkflowRunServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Delete a workflow run"""
    success = await service.delete_workflow_run(run_id)
    if not success:
        raise HTTPException(status_code=404, detail="Workflow run not found")
    return {"message": "Workflow run deleted successfully"}

@router.post("/start", response_model=AgentRunnerResponse,
             summary="Start a workflow run",
             description="""
             Called by agent-runner when an agent starts executing a workflow.
             This endpoint performs two operations:

             1. Updates the specified workflow run status to 'RUNNING'
             2. Returns the complete workflow configuration

             Required body parameters:
             - workflow_id: The ID of the workflow to execute
             - workflow_run_id: The ID of the workflow run instance
             """)
async def start_workflow_run(
    request: StartWorkflowRunDTO,
    service: WorkflowServiceDependency,
    workflow_run_service: WorkflowRunServiceDependency,
    app_service: AppServiceDependency,
    api_key: str = Depends(verify_agent_runner_api_key)
):
    """Start workflow run and update its status to running"""
    logging_service.log_message(request.workflow_run_id, "Workflow run warmup start")
    # Check if the workflow run is already running
    workflow_run = await workflow_run_service.get_workflow_run(request.workflow_run_id)
    if workflow_run and workflow_run.status == WorkflowStatus.RUNNING:
        logging_service.log_message(request.workflow_run_id, "Workflow run already running")
        return AgentRunnerResponse(
            status=AgentRunnerStatusResponse.CANCELED,
            message="Workflow run already running",
            mcp_servers=[],
            prompt="",
            agent_instructions="",
            user_id=""
        )
    
    # Update workflow run status to running
    await workflow_run_service.update_workflow_run_status(request.workflow_run_id, WorkflowStatus.RUNNING)

    # Get workflow
    workflow = await service.get_workflow_entity_by_id(request.workflow_id)

    if not workflow:
        logging_service.log_message(request.workflow_run_id, "Workflow run warmup failed, workflow not found")
        return AgentRunnerResponse(
            status=AgentRunnerStatusResponse.CANCELED,
            message="Workflow not found",
            mcp_servers=[],
            prompt="",
            agent_instructions="",
            user_id=""
        )

    # Process app configurations with credentials
    # Get all app configurations in one call, including workflow_id for workflow-specific credentials
    mcp_servers = await app_service.get_app_configurations_with_credentials(
        workflow.apps,
        workflow.user_id,
        str(workflow.id)  # Pass workflow_id to get workflow-specific credentials
    )

    if not mcp_servers:
        logging_service.log_message(request.workflow_run_id, "No valid apps configurations found for the workflow")
        return AgentRunnerResponse(
                status=AgentRunnerStatusResponse.CANCELED,
                message="No valid apps configurations found for the workflow",
                mcp_servers=[],
                prompt="",
                agent_instructions="",
                user_id=""
            )

    # Return the specific structure required by agent_runner
    logging_service.log_message(request.workflow_run_id, "Workflow run warmup completed")

    return AgentRunnerResponse(
        status=AgentRunnerStatusResponse.READY,
        message="Workflow run warmup completed",
        mcp_servers=mcp_servers,  # Now using AppConfigResponse objects
        prompt=workflow.prompt or "",
        agent_instructions=workflow.agent_instructions or "",
        user_id=workflow.user_id or ""
    )

@router.post("/logs", response_model=WorkflowLogsResponse)
async def get_workflow_logs(
    request: WorkflowLogsRequest,
    service: WorkflowRunServiceDependency,
    api_key: dict = Depends(get_admin_user)
):
    """Get logs for a specific workflow run"""
    logs, total = await service.get_workflow_logs(request.workflow_run_id, request.skip, request.limit)
    return WorkflowLogsResponse(data=logs, total=total)