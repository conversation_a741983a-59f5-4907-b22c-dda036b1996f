from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from typing import List, Optional
from app.models.blog_post import Blog<PERSON>ostCreate, BlogPostResponse, ImageUploadResponse
from app.services.blog_post_service import BlogPostService
from app.core.dependencies import get_blog_post_service
from app.core.auth import get_admin_user

router = APIRouter()

@router.post("/", response_model=BlogPostResponse)
async def create_blog_post(
    blog_post: BlogPostCreate,
    _: dict = Depends(get_admin_user),
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    return await blog_post_service.create_blog_post(blog_post)

@router.get("", response_model=List[BlogPostResponse])
async def list_blog_posts(
    skip: int = 0,
    limit: int = 10,
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    """List all blog posts with pagination"""
    return await blog_post_service.list_blog_posts(skip, limit)

@router.get("/{blog_post_id}", response_model=BlogPostResponse)
async def get_blog_post(
    blog_post_id: str,
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    """Get a blog post by ID"""
    blog_post = await blog_post_service.get_blog_post(blog_post_id)
    if not blog_post:
        raise HTTPException(status_code=404, detail="Blog post not found")
    return blog_post

@router.get("/slug/{slug}", response_model=BlogPostResponse)
async def get_blog_post_by_slug(
    slug: str,
    locale: str = "en",
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    """Get a blog post by slug and locale"""
    blog_post = await blog_post_service.get_blog_post_by_slug(slug, locale)
    if not blog_post:
        raise HTTPException(status_code=404, detail="Blog post not found")
    return blog_post

@router.put("/{blog_post_id}", response_model=BlogPostResponse)
async def update_blog_post(
    blog_post_id: str,
    blog_post: BlogPostCreate,
    _: dict = Depends(get_admin_user),
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    updated_blog_post = await blog_post_service.update_blog_post(blog_post_id, blog_post)
    if not updated_blog_post:
        raise HTTPException(status_code=404, detail="Blog post not found")
    return updated_blog_post

@router.delete("/{blog_post_id}")
async def delete_blog_post(
    blog_post_id: str,
    _: dict = Depends(get_admin_user),
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    success = await blog_post_service.delete_blog_post(blog_post_id)
    if not success:
        raise HTTPException(status_code=404, detail="Blog post not found")
    return {"message": "Blog post deleted successfully"}

@router.post("/{blog_post_id}/images", response_model=ImageUploadResponse)
async def upload_blog_post_image(
    blog_post_id: str,
    image: UploadFile = File(...),
    _: dict = Depends(get_admin_user),
    blog_post_service: BlogPostService = Depends(get_blog_post_service)
):
    """Upload an image for a blog post"""
    return await blog_post_service.upload_blog_post_image(blog_post_id, image) 