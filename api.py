from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.config.settings_loader import initialize_settings
from app.database.mongodb import MongoDB
from app.core.service_config import configure_services
from app.core.error_handlers import setup_error_handlers

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for the FastAPI application.
    Handles startup and shutdown events in the correct order.
    """
    try:
        print("Starting application initialization...")
        
        # 1. Initialize all settings first
        print("Initializing settings...")
        app.state.settings = initialize_settings()
        
        # 2. Initialize database connections
        print("Connecting to MongoDB...")
        await MongoDB.connect_db()
        
        # 3. Configure services and dependencies
        print("Configuring services...")
        configure_services(app)
        
        # 4. Set up error handlers
        print("Setting up error handlers...")
        setup_error_handlers(app)
        
        # 5. Ready to serve
        print("Application startup complete!")
        yield
        
    except Exception as e:
        print(f"Startup failed with error: {str(e)}")
        import traceback
        print("Full traceback:")
        print(traceback.format_exc())
        raise
    finally:
        print("Cleaning up resources...")
        await MongoDB.close_db()
        print("Cleanup complete")

app = FastAPI(
    title="Morrow API",
    description="A B2C platform that enables everyone to run agentic tasks through a simple UI",
    version="1.0.0",
    lifespan=lifespan,
    debug=True  # This will be overridden by settings
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Import routers after settings are initialized
from app.routers import users, workflow, apps, workflow_run, blog_posts, prompts, oauth

# Include routers
app.include_router(users.router, prefix="/v1/users", tags=["users"])
app.include_router(workflow.router, prefix="/v1/workflow", tags=["workflow"])
app.include_router(workflow_run.router, prefix="/v1/workflow-runs", tags=["workflow-runs"])
app.include_router(apps.router, prefix="/v1/apps", tags=["apps"])
app.include_router(blog_posts.router, prefix="/v1/blog-posts", tags=["blog-posts"])
app.include_router(prompts.router, prefix="/v1/prompts", tags=["prompts"])
app.include_router(oauth.router, prefix="/v1/oauth", tags=["oauth"])

if __name__ == "__main__":
    import uvicorn
    import os
    
    port = int(os.getenv("PORT", "8080"))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"Starting server on {host}:{port}")
    uvicorn.run(app, host=host, port=port, log_level="info")
