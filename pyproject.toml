[tool.poetry]
name = "backend"
version = "0.1.0"
description = "Morrow API - A B2C platform that enables everyone to run agentic tasks through a simple UI"
authors = ["Nadav <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
agno = "1.2.13"
fastapi = "^0.110.0"
uvicorn = "^0.27.1"
pydantic = {extras = ["email"], version = "^2.6.3"}
pydantic-settings = "^2.2.1"
anthropic = "0.49.0"
openai = "^1.15.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-dotenv = "^1.0.1"
pymongo = {version = ">=4.6.1,<5.0.0", extras = ["srv"]}
motor = ">=3.3.2"
python-multipart = "^0.0.9"
google-cloud-tasks = "^2.16.3"
google-cloud-pubsub = "^2.19.4"
google-auth = "^2.28.1"
rich = "^13.7.1"
better-exceptions = "^0.3.3"
google-cloud-storage = "^3.1.0"
aiohttp = "^3.11.18"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-asyncio = "^0.23.5"
httpx = "^0.27.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
asyncio_mode = "auto"
